import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON>solver
from ast import Subscript, Name, Load

def test_visit_subscript_returns_original_node():
    # Arrange
    resolver = Resolver(root='root', alias={})
    node = Subscript(value=Name(id='someName', ctx=Load()), slice=Name(id='someSlice', ctx=Load()), ctx=Load())
    
    # Act
    result = resolver.visit_Subscript(node)

    # Assert
    assert result is node  # The original node should be returned unchanged