import pytest
from apimd.parser import Resolver
from ast import Subscript, Constant, Name

def test_visit_subscript_with_non_name_value():
    # Arrange
    resolver = Resolver(root='root', alias={})
    non_name_value_node = Subscript(value=Constant(value=42), slice=Constant(value='test'), ctx=None)

    # Act
    result = resolver.visit_Subscript(non_name_value_node)

    # Assert
    assert result is non_name_value_node