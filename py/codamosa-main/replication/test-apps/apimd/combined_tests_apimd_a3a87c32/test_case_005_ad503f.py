import pytest
from unittest.mock import patch
from apimd.loader import _site_path

@patch("apimd.loader.find_spec")
def test_site_path_with_invalid_name(mock_find_spec):
    # Arrange: Mock find_spec to return None (simulating an invalid module name)
    mock_find_spec.return_value = None
    invalid_name = "!!!invalid-module-name###"

    # Act: Call the _site_path function with the invalid name
    result = _site_path(invalid_name)

    # Assert: Verify the function returns an empty string
    assert result == ""