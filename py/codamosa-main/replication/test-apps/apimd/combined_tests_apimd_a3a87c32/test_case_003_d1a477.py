import pytest
from unittest.mock import patch
from apimd.loader import _site_path

def test_site_path_no_submodule_search_locations():
    # Mocking 'find_spec' to return None for `submodule_search_locations`
    mock_spec = type('Spec', (object,), {'submodule_search_locations': None})
    
    with patch('apimd.loader.find_spec', return_value=mock_spec):
        module_name = "valid_module_without_search_locations"
        result = _site_path(module_name)
        
        # Assert the function returns an empty string
        assert result == ""