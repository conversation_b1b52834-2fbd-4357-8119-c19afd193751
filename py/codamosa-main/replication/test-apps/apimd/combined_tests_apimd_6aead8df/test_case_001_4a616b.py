import pytest
import os
from unittest.mock import patch, MagicMock
from apimd.loader import walk_packages

def test_walk_packages_no_matching_files(tmp_path):
    """
    Test `walk_packages` when the directory contains no Python source files (.py) or type stub files (.pyi).
    """
    # Create a temporary directory with files that do not have .py or .pyi extensions
    (tmp_path / "file1.txt").write_text("Sample text content")
    (tmp_path / "image.png").write_bytes(b"\x89PNG\r\n\x1a\n")
    (tmp_path / "script.js").write_text("// JavaScript file")
    
    # Mock input parameters
    name = "test_package"
    path = str(tmp_path)

    # Call the function and convert the result to a list
    result = list(walk_packages(name, path))

    # Assert the result is an empty iterator (no matching packages)
    assert result == []