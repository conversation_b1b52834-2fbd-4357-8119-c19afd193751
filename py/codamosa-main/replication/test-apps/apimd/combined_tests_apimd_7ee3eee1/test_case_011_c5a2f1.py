import pytest
from unittest.mock import patch
from ast import Name, Load
from apimd.parser import Resolver

def test_visit_name_invalid_alias_syntax():
    # Mock helper function `_m` correctly from its source module
    with patch("apimd.parser._m") as mock_m:
        # Setup
        mock_m.return_value = "invalid_alias"
        
        resolver = Resolver(root="test_root", alias={"invalid_alias": "Invalid Syntax Here"}, self_ty="self_type")
        test_node = Name(id="test_id", ctx=Load())

        # Ensure the alias maps to an invalid syntax
        with pytest.raises(SyntaxError):
            resolver.visit_Name(test_node)