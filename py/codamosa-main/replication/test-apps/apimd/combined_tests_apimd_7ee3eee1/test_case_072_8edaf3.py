import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON><PERSON>

def test___names_cmp_normal_behavior():
    # Setup
    test_string = "Example"
    expected_level_value = 3  # Example assumption, update based on realistic data
    parser = Parser()
    parser.level = {test_string: expected_level_value}  # Mock the level dictionary

    # Expected result
    expected_result = (expected_level_value, "example", True)  # Boolean True since "Example" is not all lowercase

    # Invocation of the mangled private method
    result = parser._Parser__names_cmp(test_string)

    # Assertion
    assert result == expected_result