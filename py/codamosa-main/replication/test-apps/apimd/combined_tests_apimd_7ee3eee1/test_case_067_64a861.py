import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON><PERSON>r, table

def test_get_const_retrieves_and_formats_constants():
    # Arrange
    parser = Parser()
    parser.const = {
        "root.constant1": "int",
        "root.constant2": "str",
        "other.constant3": "float"  # Not matching the given name
    }
    parser.root = {
        "root.constant1": "root",
        "root.constant2": "root",
        "other.constant3": "other"
    }
    parser.is_public = MagicMock(side_effect=lambda name: name in ["root.constant1", "root.constant2"])

    expected_table = table(
        "Constants", "Type",
        items=[
            ["`constant1`", "`int`"],
            ["`constant2`", "`str`"]
        ]
    )

    # Act
    result = parser._Parser__get_const("root")

    # Assert
    assert result == expected_table