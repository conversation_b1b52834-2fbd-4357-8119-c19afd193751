import pytest
from apimd.parser import Parser

def test_get_const_with_invalid_name_type():
    """
    Test the __get_const method to ensure it handles invalid non-string 'name' parameter 
    gracefully without raising exceptions and returns an empty string.
    """
    parser = Parser()  # Create an instance of the Parser class
    
    invalid_name = 123  # Using an integer instead of a string for the name parameter
    
    result = parser._Parser__get_const(invalid_name)  # Access the private method with its mangled name
    
    # Assert that the method returns an empty string when the name parameter is invalid
    assert result == ""