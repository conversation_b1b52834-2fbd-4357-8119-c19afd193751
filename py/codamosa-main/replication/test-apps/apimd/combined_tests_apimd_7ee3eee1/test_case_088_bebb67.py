import pytest

# Define a simple class for testing
class MockObject:
    def __init__(self):
        self.level1 = Level1()

class Level1:
    def __init__(self):
        self.level2 = None  # This level is intentionally set to None

# Import the function under test
from apimd.parser import _attr

def test_attr_returns_none_for_invalid_path():
    """
    Test to verify that `_attr` returns None when an invalid intermediate attribute in the path is accessed.
    """
    # Arrange: Create a mock object
    mock_obj = MockObject()
    
    # Act: Attempt to access an invalid nested attribute path
    result = _attr(mock_obj, "level1.level2.level3")
    
    # Assert: The method should return None since level2 is None
    assert result is None