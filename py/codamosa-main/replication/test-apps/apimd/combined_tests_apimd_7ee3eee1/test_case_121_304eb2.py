import pytest
from apimd.parser import doctest

def test_doctest_empty_input():
    """
    Test to ensure that the doctest function returns an empty string
    when the input is an empty string.
    """
    # Arrange: Define the input
    input_doc = ""
    
    # Act: Call the function
    result = doctest(input_doc)
    
    # Assert: Verify that the output is an empty string
    assert result == "", "Expected an empty string for empty input"