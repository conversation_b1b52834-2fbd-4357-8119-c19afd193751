import pytest
from apimd.parser import is_public_family

def test_is_public_family_private_name():
    """
    Validate the function handles the edge case of a single-level private name.
    """
    input_name = "_private"
    expected_output = False

    # Invoke the method under test
    result = is_public_family(input_name)

    # Assert the output
    assert result == expected_output, f"Expected {expected_output}, but got {result}"