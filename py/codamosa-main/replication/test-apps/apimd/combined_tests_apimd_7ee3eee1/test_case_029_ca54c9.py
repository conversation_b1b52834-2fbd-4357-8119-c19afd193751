import pytest
from unittest.mock import Mock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_imports_with_node_module_none():
    # Arrange
    parser = Parser()
    initial_alias = parser.alias.copy()  # To verify the alias remains unchanged
    root = "sample_root"

    # Mocking a node object with module=None
    node = Mock()
    node.module = None
    node.names = [Mock(name="Test", asname=None)]  # Add any required mock attributes

    # Act
    parser.imports(root, node)

    # Assert
    assert parser.alias == initial_alias, "The alias dictionary should remain unchanged when module is None."