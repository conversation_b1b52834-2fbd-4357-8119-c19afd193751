import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import loader

@patch('apimd.loader.walk_packages')
@patch('apimd.loader.isfile')
@patch('apimd.loader.Parser')
def test_loader_with_invalid_root_path(mock_parser, mock_isfile, mock_walk_packages):
    # Arrange
    mock_walk_packages.side_effect = OSError("Invalid directory path.")  # Simulate invalid directory access
    mock_isfile.return_value = False  # No files to check
    mock_parser.new.return_value = MagicMock()  # Mock Parser instance

    root = "/invalid/root"
    pwd = ""
    link = False
    level = 0
    toc = False

    # Act & Assert
    with pytest.raises(OSError, match="Invalid directory path."):
        loader(root, pwd, link, level, toc)