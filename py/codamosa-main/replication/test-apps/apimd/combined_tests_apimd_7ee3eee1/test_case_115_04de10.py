import pytest
from apimd.parser import code

def test_code_with_normal_text_no_special_characters():
    # Input: A string with normal text and no '|' or '&'
    input_text = "normaltext"
    # Expected Output: The string wrapped in backticks
    expected_output = "`normaltext`"
    
    # Execute the method
    result = code(input_text)
    
    # Assertion to validate the output
    assert result == expected_output, f"Expected {expected_output!r}, but got {result!r}"