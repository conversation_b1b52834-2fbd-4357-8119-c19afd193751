import ast
import pytest
from apimd.parser import _type_name, _e_type, const_type

def test_const_type_with_homogeneous_tuple():
    # Create an AST node of type 'Tuple' with homogeneous elements (e.g., integers)
    tuple_node = ast.Tuple(elts=[ast.Constant(value=1), ast.Constant(value=2), ast.Constant(value=3)])
    
    # Call the function under test
    result = const_type(tuple_node)
    
    # Assert the expected result
    assert result == 'tuple[int]', f"Expected 'tuple[int]' but got {result}"