import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_get_const_with_empty_const():
    # Arrange: Create a Parser instance with an empty const dictionary
    parser = Parser()
    parser.const = {}  # Simulate the empty 'self.const'
    parser.root = {}   # 'root' also needs to be defined to avoid KeyError
    parser.is_public = MagicMock(return_value=False)  # Mock is_public method

    # Act: Call the __get_const method with a sample name
    result = parser._Parser__get_const("sample_name")

    # Assert: The result should be an empty string
    assert result == ""