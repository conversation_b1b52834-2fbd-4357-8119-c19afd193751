import pytest
from unittest.mock import <PERSON>Mock
from typing import List
from apimd.parser import walk_body
from ast import stmt

def test_walk_body_simple_statements():
    # Arrange
    mock_stmt_1 = MagicMock(spec=stmt)  # Mock statement node
    mock_stmt_2 = MagicMock(spec=stmt)  # Mock another statement node
    body: List[stmt] = [mock_stmt_1, mock_stmt_2]  # Sequence of simple statement nodes

    # Act
    result = list(walk_body(body))  # Convert generator output to a list

    # Assert
    assert result == body, "The output should match the input sequence of nodes when no recursive calls are made."