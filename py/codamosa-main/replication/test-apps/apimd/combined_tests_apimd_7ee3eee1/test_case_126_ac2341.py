import pytest
from apimd.parser import _table_cell

def test_table_cell_with_special_characters():
    # Test input containing special characters and spaces
    input_items = ['hello', 'world!', 'a b c']
    
    # Expected Markdown-formatted string
    expected_output = '| hello | world! | a b c |'
    
    # Perform the test
    result = _table_cell(input_items)
    
    # Assert the output matches the expected result
    assert result == expected_output