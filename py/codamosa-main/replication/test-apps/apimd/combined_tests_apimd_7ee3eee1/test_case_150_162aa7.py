import pytest
from unittest.mock import mock_open, patch
from apimd.loader import _read

def test_read_valid_file():
    # Arrange
    mock_file_content = "This is a test file."
    mock_path = "test_file.txt"
    
    # Mocking open to simulate file reading
    with patch("builtins.open", mock_open(read_data=mock_file_content)) as mocked_file:
        # Act
        result = _read(mock_path)
    
        # Assert
        mocked_file.assert_called_once_with(mock_path, "r")
        assert result == mock_file_content