import pytest
from unittest.mock import MagicMock, patch
from apimd.loader import loader

@patch("apimd.loader.walk_packages")
@patch("apimd.loader.isfile")
@patch("apimd.loader._read")
@patch("apimd.loader.Parser")
def test_loader_with_valid_python_files(mock_parser, mock_read, mock_isfile, mock_walk_packages):
    # Arrange
    root = "/path/to/root"
    pwd = "/path/to/pwd"
    link = True
    level = 1
    toc = True
    
    # Mock setup for Parser
    parser_instance = MagicMock()
    mock_parser.new.return_value = parser_instance
    
    # Mock files returned by walk_packages
    mock_walk_packages.return_value = [("module1", "/path/to/module1"), ("module2", "/path/to/module2")]
    
    # Mock isfile to simulate existing .py files
    mock_isfile.side_effect = lambda path: path in ["/path/to/module1.py", "/path/to/module2.py"]
    
    # Mock file content returned by _read
    mock_read.side_effect = lambda path: f"content of {path}"
    
    # Act
    result = loader(root, pwd, link, level, toc)
    
    # Assert
    assert mock_parser.new.called_once_with(link, level, toc)
    mock_walk_packages.assert_called_once_with(root, pwd)
    expected_calls = [
        (("module1", "content of /path/to/module1.py"),),
        (("module2", "content of /path/to/module2.py"),)
    ]
    assert parser_instance.parse.call_args_list == expected_calls
    assert parser_instance.compile.call_count == 1
    assert result == parser_instance.compile.return_value