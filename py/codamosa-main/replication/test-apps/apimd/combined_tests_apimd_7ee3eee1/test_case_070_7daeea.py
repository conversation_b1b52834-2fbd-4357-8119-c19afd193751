import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_get_const_no_matching_keys():
    # Mocking the Parser instance
    parser = Parser()
    parser.root = {
        "existing.const": "matching.name"
    }  # Simulated content of self.root with non-matching value for the given name
    parser.const = {
        "existing.const": "Type1"
    }  # Simulated constants with keys present in parser.root
    parser.is_public = MagicMock(return_value=True)  # Assume all constants are public

    # Execute the method under test
    result = parser._Parser__get_const("nonexistent.name")

    # Assert that the result is an empty string
    assert result == ""