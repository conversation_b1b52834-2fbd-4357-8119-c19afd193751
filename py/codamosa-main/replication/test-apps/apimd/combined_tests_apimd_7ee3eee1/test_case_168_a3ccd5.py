import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import _load_module
from types import ModuleType

def test_load_module_parent_import_error():
    # Prepare inputs
    name = "testmodule.submodule"
    path = "/path/to/testmodule/submodule.py"
    mock_parser = MagicMock()

    # Mock for `parent` function to return testmodule
    def mock_parent(name):
        return name.split('.')[0]

    # Mock __import__ to raise ImportError for the parent module
    with patch('apimd.loader.parent', new=mock_parent):
        with patch('builtins.__import__', side_effect=ImportError):
            result = _load_module(name, path, mock_parser)

    # Assert the method returns False, as parent import will fail
    assert result is False