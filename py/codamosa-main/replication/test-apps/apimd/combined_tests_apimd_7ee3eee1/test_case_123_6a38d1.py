import pytest
from apimd.parser import _table_cell

def test_table_cell_with_multiple_non_empty_strings():
    # Input: Iterable of non-empty strings
    input_items = ['apple', 'banana', 'cherry']
    
    # Expected Output
    expected_output = '| apple | banana | cherry |'
    
    # Call the method under test
    result = _table_cell(input_items)
    
    # Assert the output matches the expected Markdown format
    assert result == expected_output