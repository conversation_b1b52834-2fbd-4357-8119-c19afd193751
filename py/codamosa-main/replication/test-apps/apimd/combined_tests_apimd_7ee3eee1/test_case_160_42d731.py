import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import _site_path

def test_site_path_valid_module():
    # Patch the 'find_spec' function used within '_site_path'
    with patch('apimd.loader.find_spec') as mock_find_spec:
        # Mock the return value to simulate a valid module with submodule_search_locations
        mock_spec = MagicMock()
        mock_spec.submodule_search_locations = ["/mocked/site-packages/some_module"]
        mock_find_spec.return_value = mock_spec
        
        # Call the function with a valid module name
        result = _site_path("some_module")
        
        # Assert that the result is the directory path of the module
        assert result == "/mocked/site-packages"
        
        # Verify that the find_spec was called correctly
        mock_find_spec.assert_called_once_with("some_module")