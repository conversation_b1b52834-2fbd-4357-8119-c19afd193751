import pytest
import ast
from unittest.mock import patch
from apimd.parser import const_type

# Mock the behavior of _e_type for testing
@patch("apimd.parser._e_type")
def test_const_type_dict_node(mock_e_type):
    # Define mock return values for _e_type
    def mock_e_type_side_effect(keys, values):
        key_types = "str"  # Both keys are strings
        value_types = "int | str"  # Values are a mix of int and string
        return f"[{key_types}, {value_types}]"
    
    mock_e_type.side_effect = mock_e_type_side_effect

    # Create a mocked AST 'Dict' node
    dict_node = ast.Dict(
        keys=[
            ast.Constant(value="key1"),    # A constant key
            ast.Constant(value="key2")     # Another constant key
        ],
        values=[
            ast.Constant(value=42),        # A constant value
            ast.Constant(value="string")   # Another constant value
        ]
    )

    # Expected type string based on the values provided in the dict node
    expected_type = "dict[str, int | str]"

    # Call the method under test
    result_type = const_type(dict_node)

    # Check if the returned type matches the expected type
    assert result_type == expected_type