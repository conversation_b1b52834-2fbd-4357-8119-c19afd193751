import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>, Resolver
from ast import Subscript, Attribute, Name, Call, Index


@pytest.fixture
def mock_resolver(monkeypatch):
    """Fixture to mock the behavior of the Resolver class."""
    mocked_resolver = MagicMock(spec=Resolver)
    mocked_resolver.generic_visit = MagicMock(side_effect=lambda node: node)
    mocked_resolver.visit = MagicMock(side_effect=lambda node: node)

    # Mock the Resolver class to return the mocked_resolver instance
    def mock_resolver_class(*args, **kwargs):
        return mocked_resolver

    monkeypatch.setattr("apimd.parser.Resolver", mock_resolver_class)
    return mocked_resolver


@pytest.fixture
def mock_unparse(monkeypatch):
    """Fixture to mock the unparse function."""
    monkeypatch.setattr("apimd.parser.unparse", lambda node: "resolved_expression")


def test_resolve_complex_nested_annotation(mock_resolver, mock_unparse):
    """
    Test the `resolve` method for a complex nested annotation.

    Purpose:
        Verify the method resolves names correctly in complex nested annotations.
    """
    # Arrange
    parser_instance = Parser()

    root = "my_module"
    # Construct a complex expression as an AST node
    node = Subscript(
        value=Attribute(value=Name(id="typing", ctx=None), attr="List", ctx=None),
        slice=Index(value=Call(func=Name(id="CustomType", ctx=None), args=[], keywords=[])),
        ctx=None,
    )

    expected_result = "resolved_expression"

    # Act
    result = parser_instance.resolve(root, node)

    # Assert
    assert result == expected_result
    mock_resolver.generic_visit.assert_called_once_with(node)
    mock_resolver.visit.assert_called_once_with(node)