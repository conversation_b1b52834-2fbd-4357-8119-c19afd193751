import pytest
from apimd.parser import is_public_family

def is_magic(name: str) -> bool:
    """Helper function to identify magic names"""
    return name.startswith('__') and name.endswith('__')

@pytest.mark.parametrize("name, expected", [
    ("__init__.module", True)  # Magic name doesn't disqualify the public nature
])
def test_is_public_family_with_magic_name(name, expected):
    assert is_public_family(name) == expected