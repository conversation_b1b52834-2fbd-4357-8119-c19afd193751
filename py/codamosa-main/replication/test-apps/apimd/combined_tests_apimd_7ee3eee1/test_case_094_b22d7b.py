import pytest
from apimd.parser import parent

def test_parent_with_level_zero():
    # Test input where level is set to 0
    name = "module.submodule.ClassName"
    level = 0
    
    # Expected output: the entire name string remains unchanged
    expected = name

    # Call the method under test
    result = parent(name, level=level)
    
    # Assert the result matches the expected output
    assert result == expected, f"Expected '{expected}', but got '{result}'"