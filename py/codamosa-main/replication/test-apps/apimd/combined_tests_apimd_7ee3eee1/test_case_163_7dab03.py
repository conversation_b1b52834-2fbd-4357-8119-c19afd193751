import pytest
from unittest.mock import patch
from apimd.loader import _site_path

def test_site_path_with_empty_string():
    # Arrange: input as an empty string
    module_name = ""
    
    # Act: Mock find_spec to return None when called with an empty module name
    with patch("apimd.loader.find_spec", return_value=None) as mock_find_spec:
        result = _site_path(module_name)
    
    # Assert: Check that the result is an empty string
    assert result == ""
    # Ensure find_spec was called with the correct argument
    mock_find_spec.assert_called_once_with(module_name)