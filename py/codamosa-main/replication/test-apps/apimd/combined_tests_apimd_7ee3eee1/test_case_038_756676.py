import pytest
from unittest.mock import Mock, MagicMock
from apimd.parser import Parser
import ast

def create_arg_mock(arg_name, annotation_str):
    """Helper function to create a mock argument with annotations."""
    arg_mock = MagicMock()
    arg_mock.arg = arg_name
    arg_mock.annotation = ast.Constant(value=annotation_str)  # Use AST Constant to simulate expr
    return arg_mock

def test_func_api_with_varargs_and_kwargs():
    # Arrange
    parser = Parser()
    parser.doc = {"test_func": ""}  # Prepopulate the doc dictionary

    mock_arguments = Mock()
    # Simulating arguments structure with positional, *args, **kwargs
    mock_arguments.posonlyargs = []
    mock_arguments.args = [
        create_arg_mock("x", "int"),
        create_arg_mock("y", "str")
    ]
    mock_arguments.defaults = [None, None]
    mock_arguments.kwonlyargs = [
        create_arg_mock("z", "float")
    ]
    mock_arguments.kw_defaults = [None]
    mock_arguments.vararg = create_arg_mock("args", "list")
    mock_arguments.kwarg = create_arg_mock("kwargs", "dict")
    
    mock_root = "mock_root"
    mock_name = "test_func"
    mock_returns = ast.Constant(value="str")  # Use AST Constant for the return type
    
    # Act
    parser.func_api(
        root=mock_root,
        name=mock_name,
        node=mock_arguments,
        returns=mock_returns,
        has_self=False,
        cls_method=False
    )

    # Assert
    # Ensuring the generated API documentation includes *args and **kwargs with annotations
    generated_doc = parser.doc[mock_name]
    assert "*args" in generated_doc
    assert "**kwargs" in generated_doc
    assert "int" in generated_doc
    assert "str" in generated_doc
    assert "float" in generated_doc
    assert "return" in generated_doc
    assert "str" in generated_doc