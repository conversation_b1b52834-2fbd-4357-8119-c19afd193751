import pytest
import ast
from unittest.mock import patch
from apimd.parser import const_type

def test_const_type_call_node_builtin_type():
    # Create an AST node representing a Call to a built-in type (e.g. int)
    call_node = ast.Call(
        func=ast.Name(id="int", ctx=ast.Load()),
        args=[ast.Constant(value=5)],  # Example argument to the function
        keywords=[]
    )

    # Mock the unparse function to return the correct function name
    with patch("apimd.parser.unparse", return_value="int"):
        # Call the method under test
        result = const_type(call_node)

    # Assert that the output matches the name of the built-in type
    assert result == "int"