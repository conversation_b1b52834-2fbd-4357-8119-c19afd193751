import pytest
from apimd.parser import <PERSON><PERSON><PERSON>

def test_post_init_toc_truthiness():
    """
    Test the __post_init__ method of the Parser class to ensure the 'link' attribute
    is updated based on the truthiness of the 'toc' attribute.
    """
    # Test case for falsy 'toc' - 0
    parser_with_falsy_toc = Parser()
    parser_with_falsy_toc.toc = 0  # Manually set to a falsy value
    parser_with_falsy_toc.__post_init__()  # Call __post_init__ explicitly
    assert parser_with_falsy_toc.link is True, "Expected 'link' to remain True when 'toc' is falsy."

    # Test case for falsy 'toc' - empty list
    parser_with_empty_list = Parser()
    parser_with_empty_list.toc = []  # Empty list is falsy
    parser_with_empty_list.__post_init__()  # Call __post_init__ explicitly
    assert parser_with_empty_list.link is True, "Expected 'link' to remain True when 'toc' is an empty list."

    # Test case for truthy 'toc' - non-empty list
    parser_with_truthy_toc = Parser()
    parser_with_truthy_toc.toc = [1]  # Non-empty list is truthy
    parser_with_truthy_toc.__post_init__()  # Call __post_init__ explicitly
    assert parser_with_truthy_toc.link is True, "Expected 'link' to be True when 'toc' is truthy."