import pytest
from unittest.mock import Mock
from ast import Attribute, Name, Load
from apimd.parser import Resolver

def test_visit_attribute_empty_string():
    # Arrange - Create a mock Attribute node
    mock_node = Mock(spec=Attribute)
    mock_node.value = Name(id='typing', ctx=Load())  # Mimic `node.value` as `Name`
    mock_node.attr = ""  # Set `node.attr` to empty string
    
    resolver = Resolver(root="", alias={}, self_ty="")
    
    # Act - Call the method under test
    result = resolver.visit_Attribute(mock_node)
    
    # Assert - Validate the resulting Name node
    assert isinstance(result, Name)
    assert result.id == ""
    assert isinstance(result.ctx, Load)