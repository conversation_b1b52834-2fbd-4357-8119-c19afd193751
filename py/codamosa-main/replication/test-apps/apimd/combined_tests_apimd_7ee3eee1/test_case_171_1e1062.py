import pytest
from unittest.mock import Mock, patch, call
from apimd.loader import loader

def test_loader_handles_stub_files_without_corresponding_py():
    """
    Test `loader` function to ensure it handles directories containing
    only `.pyi` files without corresponding `.py` files.
    """
    # Mocks
    mock_walk_packages = Mock(return_value=[("module_name", "module_path")])
    mock_isfile = Mock(side_effect=lambda path: path.endswith(".pyi"))
    mock_logger_debug = Mock()
    mock_logger_warning = Mock()
    mock_parser = Mock()
    mock_read = Mock(return_value="parsed content")
    
    class MockParser:
        @staticmethod
        def new(link, level, toc):
            return mock_parser

    # Patching dependencies
    with patch("apimd.loader.walk_packages", mock_walk_packages), \
         patch("apimd.loader.isfile", mock_isfile), \
         patch("apimd.loader.logger.debug", mock_logger_debug), \
         patch("apimd.loader.logger.warning", mock_logger_warning), \
         patch("apimd.loader.Parser", MockParser), \
         patch("apimd.loader._read", mock_read):
        
        # Call tested function
        result = loader(root="/mock/root", pwd="/mock/pwd", link=True, level=1, toc=False)
        
        # Assertions on mocked dependencies
        mock_walk_packages.assert_called_once_with("/mock/root", "/mock/pwd")
        mock_isfile.assert_has_calls([
            call("module_path.py"),
            call("module_path.pyi"),
        ])
        mock_logger_debug.assert_any_call("module_name <= module_path.pyi")
        mock_parser.parse.assert_called_once_with("module_name", "parsed content")
        mock_parser.compile.assert_called_once()

        # Verify output
        assert result == mock_parser.compile.return_value  # Ensure the compiled output is returned