import pytest
from apimd.parser import is_magic

def test_is_magic_with_multiple_segments():
    # Input with multiple '.' separators
    input_name = "module.submodule.__magic__"
    
    # Expected outcome
    expected_output = True
    
    # Actual outcome
    actual_output = is_magic(input_name)
    
    # Assertion
    assert actual_output == expected_output, f"Expected {expected_output} but got {actual_output}"