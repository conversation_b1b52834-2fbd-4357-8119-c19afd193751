import pytest
from apimd.parser import Parser

def test_names_cmp_empty_string():
    # Arrange
    parser = Parser()
    parser.level = {}  # Mock the `level` dictionary
    empty_string = ""
    # Get the mangled name for the private method
    mangled_method = parser._Parser__names_cmp

    # Act & Assert
    with pytest.raises(KeyError):  # Expecting KeyError since empty_string is not in `level`
        mangled_method(empty_string)