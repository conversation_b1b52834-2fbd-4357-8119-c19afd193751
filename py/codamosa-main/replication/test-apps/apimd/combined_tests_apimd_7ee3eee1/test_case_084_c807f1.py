import pytest

class DummyObject:
    """Dummy class to simulate an object with attributes."""
    def __init__(self):
        self.name = "TestName"

def test_attr_single_level():
    # Arrange
    from apimd.parser import _attr  # Import the method under test
    obj = DummyObject()  # Object to test with a 'name' attribute
    
    # Act
    result = _attr(obj, "name")
    
    # Assert
    assert result == "TestName", "The _attr method failed to retrieve the single-level attribute."