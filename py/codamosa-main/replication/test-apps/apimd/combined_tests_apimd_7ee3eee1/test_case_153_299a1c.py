import pytest
from unittest.mock import mock_open, patch

def test_read_file_permission_error():
    # Arrange
    from apimd.loader import _read
    restricted_path = "/path/to/restricted_file"

    # Mock the open function to raise a PermissionError
    with patch("builtins.open", mock_open()) as mocked_open:
        mocked_open.side_effect = PermissionError("Permission denied")

        # Act & Assert
        with pytest.raises(PermissionError, match="Permission denied"):
            _read(restricted_path)