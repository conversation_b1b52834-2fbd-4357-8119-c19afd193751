import pytest
from unittest.mock import MagicMock, patch
from apimd.parser import <PERSON>rser

@pytest.fixture
def parser():
    parser = Parser()
    parser.imp = {
        "module": {"module.func1", "module.func2"},
    }
    parser.root = {
        "module.func1": "module",
        "module.func2": "module",
    }
    return parser

@patch("apimd.parser.is_public_family")
def test_is_public_with_valid_public_name(mock_is_public_family, parser):
    # Setup
    mock_is_public_family.return_value = True
    parser.doc = {}
    parser.const = {}
    s = "module.func1"

    # Action
    result = parser.is_public(s)

    # Assertion
    assert result is True
    mock_is_public_family.assert_not_called()  # No need to call since it's in __all__