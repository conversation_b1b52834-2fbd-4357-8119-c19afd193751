from unittest.mock import Magic<PERSON>ock
import pytest
from apimd.parser import <PERSON><PERSON><PERSON>
from typing import Sequence
from collections import namedtuple

# Create a mock arg type for testing
arg = namedtuple("arg", ["arg", "annotation"])

@pytest.fixture
def parser_instance():
    """Fixture to create an instance of the Parser."""
    return Parser()

def test_func_ann_with_star_annotation(parser_instance):
    """
    Test the func_ann method to ensure it behaves correctly
    for arguments annotated with '*'.
    """
    root = "example_root"
    # Define arguments including one with '*' annotation
    args = [
        arg("arg1", "Annotation1"),
        arg("*", None),  # argument annotated with '*'
        arg("arg2", "Annotation2"),
    ]
    has_self = False
    cls_method = False

    # Mock the resolve method to return mock annotation strings
    parser_instance.resolve = MagicMock(side_effect=lambda root, annotation, self_ty="": f"Resolved({annotation})")

    # Call func_ann and collect results as a list
    result = list(parser_instance.func_ann(root, args, has_self=has_self, cls_method=cls_method))

    # Assertions
    assert result == ["Resolved(Annotation1)", "", "Resolved(Annotation2)"]