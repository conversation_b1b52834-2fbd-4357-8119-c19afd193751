import pytest
from apimd.parser import esc_underscore

def test_esc_underscore_single_underscore():
    # Input string with exactly one underscore
    input_str = "hello_world"
    # Expected output is the same string without modification
    expected_output = "hello_world"
    
    # Call the method under test
    result = esc_underscore(input_str)
    
    # Assertion
    assert result == expected_output, f"Expected '{expected_output}', but got '{result}'"