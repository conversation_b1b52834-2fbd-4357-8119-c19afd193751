from unittest.mock import Mock, patch
import pytest
from apimd.parser import Parser


@pytest.fixture
def parser_instance():
    """Fixture to create a Parser instance with mock data."""
    instance = Parser()
    instance.doc = {
        "root.key1": "value1",
        "root.key2": "value2"
    }
    instance.docstring = {}
    return instance


def test_load_docstring_getdoc_returns_none(parser_instance):
    """Test the behavior when 'getdoc' returns None for all attributes."""
    mock_module = Mock()  # Create a mock module 
    
    # Mock _attr to always return an object
    with patch("apimd.parser._attr", return_value=Mock()) as mock_attr:
        # Mock getdoc to always return None
        with patch("apimd.parser.getdoc", return_value=None):
            parser_instance.load_docstring("root", mock_module)

            # Ensure _attr was called for each key in self.doc starting with 'root'
            mock_attr.assert_any_call(mock_module, "key1")
            mock_attr.assert_any_call(mock_module, "key2")
            
            # Assert that since getdoc returns None, docstring remains unchanged
            assert parser_instance.docstring == {}