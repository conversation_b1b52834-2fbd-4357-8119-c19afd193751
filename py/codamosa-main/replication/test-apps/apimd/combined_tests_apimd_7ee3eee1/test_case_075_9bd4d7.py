import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON><PERSON>

def test___names_cmp_case_sensitivity():
    # Arrange
    parser = Parser()
    parser.level = {"TestKey": 10}  # Mocked level dictionary
    test_string = "TestKey"

    # Act
    result = parser._Parser__names_cmp(test_string)  # Accessing the private method using name mangling

    # Assert
    assert result == (10, "testkey", True)