import pytest
import ast
from apimd.parser import walk_body

def test_walk_body_with_try_statement():
    # Create a Python AST for a Try statement with body, handlers, orelse, and finalbody
    try_node = ast.Try(
        body=[
            ast.Expr(value=ast.Constant(value="try-body-stmt"))
        ],
        handlers=[
            ast.ExceptHandler(
                type=ast.Name(id="Exception", ctx=ast.Load()),
                name="e",
                body=[ast.Expr(value=ast.Constant(value="except-handler-stmt"))]
            )
        ],
        orelse=[
            ast.Expr(value=ast.Constant(value="orelse-stmt"))
        ],
        finalbody=[
            ast.Expr(value=ast.Constant(value="finalbody-stmt"))
        ]
    )

    # Use a single-element list as the body containing the Try node
    body = [try_node]

    # Collect all yielded nodes
    yielded_nodes = list(walk_body(body))

    # Extract the `value` attribute of ast.Constant nodes for comparison
    actual_values = [
        node.value.value for node in yielded_nodes
        if isinstance(node, ast.Expr) and isinstance(node.value, ast.Constant)
    ]

    # Define the expected order of traversal
    expected_values = [
        "try-body-stmt",
        "except-handler-stmt",
        "orelse-stmt",
        "finalbody-stmt",
    ]

    # Assert the actual values match the expected values
    assert actual_values == expected_values, f"Expected {expected_values}, but got {actual_values}"