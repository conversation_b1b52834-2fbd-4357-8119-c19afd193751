import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import loader

def test_loader_with_empty_directory():
    # Test input for an empty directory
    root = "/empty_directory"
    pwd = "/empty_directory"
    link = False
    level = 1
    toc = False

    # Mock dependencies in the loader function
    with patch("apimd.loader.walk_packages", return_value=[]) as mock_walk_packages, \
         patch("apimd.loader.Parser") as mock_parser:
        
        # Setup mock behavior for Parser
        mock_parser_instance = MagicMock()
        mock_parser.new.return_value = mock_parser_instance
        mock_parser_instance.compile.return_value = "compiled_output"

        # Call the method under test
        result = loader(root, pwd, link, level, toc)

        # Validate that walk_packages was called correctly
        mock_walk_packages.assert_called_once_with(root, pwd)
        
        # Validate that <PERSON><PERSON><PERSON> was initialized correctly
        mock_parser.new.assert_called_once_with(link, level, toc)
        
        # Validate that no parsing or module loading occurred
        mock_parser_instance.parse.assert_not_called()

        # Validate the return value
        assert result == "compiled_output"