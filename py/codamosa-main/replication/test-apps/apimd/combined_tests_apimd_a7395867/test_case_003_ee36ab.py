import pytest
from unittest.mock import Mock
from types import ModuleType
from apimd.parser import Parser

@pytest.fixture
def parser_with_doc():
    """Fixture to provide a Parser with an initialized doc dictionary."""
    parser = Parser()
    parser.doc = {
        "root.test_function": "Some dummy doc placeholder"
    }
    parser.docstring = {}
    return parser

def test_load_docstring_no_docstring_in_module(parser_with_doc):
    # Arrange
    root = "root"
    mock_module = Mock(spec=ModuleType)
    mock_function = Mock()
    mock_function.__doc__ = None  # Simulate no docstring for attribute
    mock_module.test_function = mock_function

    # Act
    parser_with_doc.load_docstring(root, mock_module)

    # Assert
    assert "root.test_function" not in parser_with_doc.docstring, \
        "docstring should not be updated when the attribute has no docstring."