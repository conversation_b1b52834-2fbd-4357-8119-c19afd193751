import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_find_alias_empty_doc():
    # Arrange: Create parser instance and set empty doc and alias
    parser = Parser()
    parser.doc = {}
    parser.docstring = {}
    parser.root = {}
    parser.level = {}
    parser.const = {}
    parser.alias = {}

    # Act: Invoke the method under test
    parser._Parser__find_alias()  # Calling the private method

    # Assert: Ensure no modifications are made to the empty dictionaries
    assert parser.doc == {}
    assert parser.docstring == {}
    assert parser.root == {}
    assert parser.level == {}
    assert parser.const == {}