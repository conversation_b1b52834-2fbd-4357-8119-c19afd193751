import pytest
from apimd.parser import <PERSON><PERSON><PERSON>

def test_find_alias_with_no_matching_aliases():
    # Arrange
    parser = Parser()
    parser.alias = {
        "alias_1": "non_existent_key_1",
        "alias_2": "non_existent_key_2"
    }
    # Mock dependencies
    parser.doc = {
        "some_key": "some_value"
    }
    parser.docstring = {
        "some_key": "some_docstring"
    }
    parser.root = {
        "some_key": "some_root"
    }
    parser.level = {
        "some_key": 1
    }
    parser.const = {
        "some_key": "some_const"
    }
    # Save the state of dictionaries before calling the method
    doc_before = parser.doc.copy()
    docstring_before = parser.docstring.copy()
    root_before = parser.root.copy()
    level_before = parser.level.copy()
    const_before = parser.const.copy()

    # Act
    parser._Parser__find_alias()  # Call the private method

    # Assert
    assert parser.doc == doc_before, "doc should remain unchanged"
    assert parser.docstring == docstring_before, "docstring should remain unchanged"
    assert parser.root == root_before, "root should remain unchanged"
    assert parser.level == level_before, "level should remain unchanged"
    assert parser.const == const_before, "const should remain unchanged"