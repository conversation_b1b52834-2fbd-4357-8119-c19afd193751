import pytest

class NestedObject:
    """Helper class to create objects with nested attributes."""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

def test_attr_retrieves_nested_attribute():
    # Create a nested object for testing
    nested_obj = NestedObject(
        level1=NestedObject(
            level2=NestedObject(
                level3="final_value"
            )
        )
    )

    # Test the _attr function
    from apimd.parser import _attr
    attribute_path = "level1.level2.level3"
    result = _attr(nested_obj, attribute_path)
    
    # Assert that the final nested value is returned correctly
    assert result == "final_value"