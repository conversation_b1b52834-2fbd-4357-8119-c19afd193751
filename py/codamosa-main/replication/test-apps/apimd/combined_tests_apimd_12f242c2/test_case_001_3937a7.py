import pytest
from apimd.parser import Resolver

def test_resolver_init():
    # Arrange
    root = "my_module"
    alias = {"Alias1": "Alias1_Expanded", "Alias2": "Alias2_Expanded"}
    self_ty = "MySelfType"
    
    # Act
    resolver = Resolver(root, alias, self_ty)
    
    # Assert
    assert resolver.root == root, "Root attribute was not initialized correctly."
    assert resolver.alias == alias, "Alias attribute was not initialized correctly."
    assert resolver.self_ty == self_ty, "Self type attribute was not initialized correctly."