import pytest
from apimd.parser import parent

def test_parent_without_delimiter():
    # Input: name string without any '.' characters
    input_name = "simple_name"
    expected_output = "simple_name"
    
    # Call the `parent` method with the input
    result = parent(name=input_name, level=1)
    
    # Assert the result matches the expected output
    assert result == expected_output, f"Expected '{expected_output}', but got '{result}'"