import pytest
from apimd.parser import parent

def test_parent_level_exceeds_dots():
    # Test case: level is greater than the number of '.' characters in the name
    name = "singlecomponent"
    level = 3  # Greater than the number of '.' in `name`

    # Expected: Since there are no '.' to split, the entire name is returned
    expected_result = "singlecomponent"

    # Call the method and check the result
    result = parent(name=name, level=level)
    assert result == expected_result, f"Expected '{expected_result}' but got '{result}'"