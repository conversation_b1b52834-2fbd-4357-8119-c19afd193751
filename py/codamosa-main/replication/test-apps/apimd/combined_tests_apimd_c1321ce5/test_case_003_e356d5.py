import pytest
import ast
from apimd.parser import walk_body

def test_walk_body_nested_if_statement():
    # Create AST nodes for testing
    pass_node1 = ast.Pass()  # A simple Pass statement
    pass_node2 = ast.Pass()
    pass_node3 = ast.Pass()

    # Create a nested If statement
    inner_if_node = ast.If(
        test=ast.Constant(value=True),
        body=[pass_node1],
        orelse=[pass_node2]
    )
    outer_if_node = ast.If(
        test=ast.Constant(value=False),
        body=[inner_if_node],
        orelse=[pass_node3]
    )

    # Input is a sequence containing the top-level If statement
    body = [outer_if_node]

    # Call the walk_body method
    result = list(walk_body(body))

    # Assertions
    # Verify that all nodes (including nested ones) are yielded
    assert result == [pass_node1, pass_node2, pass_node3]