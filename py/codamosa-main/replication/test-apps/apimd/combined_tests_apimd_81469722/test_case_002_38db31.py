import sys
from ast import Constant
import pytest

# Ensure the module path is correct; replace '/path/to/apimd' with the actual path to `apimd`
# Uncomment the following block if there are import issues
"""
import os
sys.path.insert(0, '/path/to/apimd')
"""

from apimd.parser import Resolver

def test_visit_constant_non_string_value():
    # Arrange
    resolver = Resolver(root="root_module", alias={}, self_ty="")
    mock_node = Constant(value=42)  # Non-string value (integer)

    # Act
    result = resolver.visit_Constant(mock_node)

    # Assert
    assert result == mock_node  # The original node should be returned