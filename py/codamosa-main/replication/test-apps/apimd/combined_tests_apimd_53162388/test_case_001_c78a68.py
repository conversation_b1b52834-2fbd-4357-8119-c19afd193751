import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON>rse<PERSON>
from ast import arguments, arg, Constant

def test_func_api_positional_args_no_defaults():
    # Arrange
    parser = Parser()
    parser.doc = {}  # Initialize the `doc` dictionary that `func_api` modifies
    root = "module"
    name = "module.function_name"
    
    # Mock the `arguments` object
    posonlyarg1 = arg(arg='arg1', annotation=Constant(value="int", kind=None))
    posonlyarg2 = arg(arg='arg2', annotation=Constant(value="str", kind=None))
    node = arguments(posonlyargs=[posonlyarg1, posonlyarg2],
                     args=[], defaults=[], kwonlyargs=[], 
                     kw_defaults=[], vararg=None, kwarg=None)

    returns = Constant(value="None", kind=None)
    
    # Mock supporting methods
    parser.func_ann = MagicMock(return_value=["int", "str", "None"])
    parser.doc[name] = ""  # Placeholder for the documentation initialization

    # Act
    parser.func_api(root=root, name=name, node=node, returns=returns, has_self=False, cls_method=False)

    # Assert
    assert name in parser.doc, f"Expected {name} to be in parser.doc keys."
    assert "arg1" in parser.doc[name], "Positional argument 'arg1' should be included in the output."
    assert "arg2" in parser.doc[name], "Positional argument 'arg2' should be included in the output."
    assert " / " in parser.doc[name], "Positional-only separator '/' should be included for positional-only arguments."
    assert "return" in parser.doc[name], "The return type should be included in the output."