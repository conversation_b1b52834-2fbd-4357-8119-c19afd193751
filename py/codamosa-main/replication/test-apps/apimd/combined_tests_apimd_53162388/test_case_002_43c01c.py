import pytest
from unittest.mock import Mock
from apimd.parser import Parse<PERSON>
from ast import arguments, arg, Name, Constant, keyword

@pytest.fixture
def parser_instance():
    """Fixture to create a Parser instance."""
    return Parser()

def test_func_api_mixed_args_with_defaults(parser_instance):
    # Mock input data
    root = "root_module"
    name = "test_function"
    
    # Construct realistic AST arguments object
    ast_arguments = arguments(
        posonlyargs=[],
        args=[
            arg(arg="arg1", annotation=None),
            arg(arg="arg2", annotation=None)
        ],
        defaults=[
            Constant(value="default2")
        ],
        vararg=None,
        kwonlyargs=[
            arg(arg="kwarg1", annotation=None),
            arg(arg="kwarg2", annotation=None)
        ],
        kw_defaults=[
            None,
            Constant(value="default_kwarg2")
        ],
        kwarg=None
    )

    returns = None
    has_self = False
    cls_method = False

    # Initialize a mocked doc storage within Parser
    parser_instance.doc = {name: ""}

    # Call the method under test
    parser_instance.func_api(
        root, 
        name, 
        ast_arguments, 
        returns, 
        has_self=has_self, 
        cls_method=cls_method
    )

    # Assertions to validate expected behavior
    assert name in parser_instance.doc, "Function documentation entry was not created."
    doc_content = parser_instance.doc[name]

    # Check presence of each argument and defaults
    assert "arg1" in doc_content, "Positional argument 'arg1' not documented."
    assert "arg2" in doc_content, "Positional argument 'arg2' not documented."
    assert "default2" in doc_content, "Default value for 'arg2' not documented."
    assert "kwarg1" in doc_content, "Keyword-only argument 'kwarg1' not documented."
    assert "kwarg2" in doc_content, "Keyword-only argument 'kwarg2' not documented."
    assert "default_kwarg2" in doc_content, "Default value for 'kwarg2' not documented."