import pytest
from apimd.parser import Resolver

def test_resolver_initialization():
    # Given test inputs
    root = "my.module"
    alias = {"var1": "my.module.var1", "var2": "my.module.var2"}
    self_ty = "MyType"
    
    # When initializing Resolver
    resolver = Resolver(root=root, alias=alias, self_ty=self_ty)
    
    # Then assert that the attributes are set correctly
    assert resolver.root == root
    assert resolver.alias == alias
    assert resolver.self_ty == self_ty