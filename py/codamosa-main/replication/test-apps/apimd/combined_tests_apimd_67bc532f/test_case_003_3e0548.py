import pytest
from apimd.parser import Parser

def test_new_invalid_toc_type():
    # Verify that the `new` method can handle invalid `toc` values without raising an error,
    # but certain behaviors might be affected in the created object
    invalid_toc_values = ["string", 123, None, 3.14, [], {}]
    
    for invalid_toc in invalid_toc_values:
        parser = Parser.new(link=True, level=1, toc=invalid_toc)
        assert parser.toc == invalid_toc  # Ensure the `toc` parameter is set as is
        # Further assertions can be added if specific behaviors of `toc` are defined