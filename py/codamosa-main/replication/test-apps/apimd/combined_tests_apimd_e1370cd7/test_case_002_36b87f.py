import pytest
from unittest.mock import MagicMock
from apimd.parser import Resolver
from ast import Constant

def test_visit_constant_syntax_error():
    # Setup
    resolver = Resolver(root='test_root', alias={})
    invalid_node = Constant(value='5+')  # This string will raise a SyntaxError when parsed

    # Act
    result = resolver.visit_Constant(invalid_node)

    # Assert
    assert result is invalid_node, "The original node should be returned unchanged due to SyntaxError"