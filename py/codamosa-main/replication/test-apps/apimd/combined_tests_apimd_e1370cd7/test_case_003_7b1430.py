import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import Resolver
from ast import Constant

def test_visit_constant_non_string_value():
    # Create an instance of the Resolver
    resolver = Resolver(root="test_root", alias={})
    
    # Create a Constant node with a non-string value (e.g., an integer)
    non_string_node = Constant(value=42)

    # Call the visit_Constant method
    result = resolver.visit_Constant(non_string_node)

    # Assert that the result is the same as the original node
    assert result is non_string_node