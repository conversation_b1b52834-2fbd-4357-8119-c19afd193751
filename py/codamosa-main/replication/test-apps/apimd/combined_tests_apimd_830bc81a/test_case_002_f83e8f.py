import pytest
from unittest.mock import Mock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_imports_empty_node_names():
    # Arrange
    parser = Parser()
    root = "test_root"
    mock_node = Mock()
    mock_node.names = []  # Empty names list
    mock_node.module = None
    mock_node.level = None

    initial_alias = parser.alias.copy()  # Copy initial state of alias dict

    # Act
    parser.imports(root, mock_node)

    # Assert
    assert parser.alias == initial_alias, "Alias should remain unchanged when node names list is empty"