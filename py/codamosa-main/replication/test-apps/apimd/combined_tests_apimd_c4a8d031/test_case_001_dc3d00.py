import pytest
from apimd.parser import Resolver

def test_resolver_initialization():
    # Test parameters
    root = "test_root"
    alias = {"module1": "alias1", "module2": "alias2"}
    self_ty = "SelfType"
    
    # Create an instance of the Resolver class
    resolver = Resolver(root, alias, self_ty)
    
    # Assert instance variables are set correctly
    assert resolver.root == root
    assert resolver.alias == alias
    assert resolver.self_ty == self_ty