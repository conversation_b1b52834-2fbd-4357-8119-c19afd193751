import pytest
from unittest.mock import MagicMock
from ast import Attribute, Name, Load
from apimd.parser import Resolver

def test_visit_attribute_with_non_typing_name():
    # Arrange
    resolver = Resolver(root='my_module', alias={})
    non_typing_name = Name(id='non_typing', ctx=Load())
    attribute_node = Attribute(value=non_typing_name, attr='my_attribute', ctx=Load())
    
    # Act
    result = resolver.visit_Attribute(attribute_node)
    
    # Assert
    assert result is attribute_node