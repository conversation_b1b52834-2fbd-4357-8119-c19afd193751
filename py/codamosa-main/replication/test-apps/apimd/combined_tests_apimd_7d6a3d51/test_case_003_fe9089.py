import pytest
from ast import Attribute, Constant, Name, Load
from apimd.parser import Resolver

def test_visit_attribute_with_non_name_value():
    # Create an instance of Resolver
    resolver = Resolver(root='', alias={})

    # Create an Attribute node with a value that is not a Name instance (e.g., a Constant)
    non_name_value = Constant(value=42)  # A constant node
    attr_node = Attribute(value=non_name_value, attr='name', ctx=Load())

    # Call the visit_Attribute method
    result = resolver.visit_Attribute(attr_node)

    # Assert that the output is unchanged and is the same as the input node
    assert result is attr_node  # Check that the returned node is the same instance as the input