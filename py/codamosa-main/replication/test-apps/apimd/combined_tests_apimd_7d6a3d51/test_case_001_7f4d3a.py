import pytest
from unittest.mock import Mock
from ast import Attribute, Name, Load
from apimd.parser import Resolver

def test_visit_Attribute_strip_typing_prefix():
    resolver = Resolver("root", {})
    
    # Create an AST Attribute node with a 'typing' prefix
    attribute_node = Attribute(value=Name(id='typing'), attr='List')
    
    # Invoke the method under test
    result = resolver.visit_Attribute(attribute_node)
    
    # Assert that the result is a new Name node with the attribute's name
    assert isinstance(result, Name), "Expected result to be an instance of Name"
    assert result.id == 'List', "Expected the attribute name to be 'List'"
    assert isinstance(result.ctx, Load), "Expected the context of the Name to be Load"

def test_visit_Attribute_no_typing_prefix():
    resolver = Resolver("root", {})
    
    # Create an AST Attribute node without a 'typing' prefix
    attribute_node = Attribute(value=Name(id='other'), attr='List')
    
    # Invoke the method under test
    result = resolver.visit_Attribute(attribute_node)
    
    # Assert that the result is the same as the input node
    assert result == attribute_node, "Expected the result to be the same as the input node"