import pytest
from apimd.parser import _m

def test_m_with_empty_and_non_empty_strings():
    # Test input containing both empty and non-empty strings
    input_strings = ['', 'submodule', '', 'package']
    
    # Expected output: non-empty strings concatenated with dots
    expected_output = 'submodule.package'
    
    # Call the method under test
    result = _m(*input_strings)
    
    # Assert that the result matches the expected output
    assert result == expected_output