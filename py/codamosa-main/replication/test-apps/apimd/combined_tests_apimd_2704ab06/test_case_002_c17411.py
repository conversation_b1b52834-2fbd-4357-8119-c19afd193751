import pytest
from apimd.parser import Parser

def test_compile_with_empty_doc():
    # Arrange: Create a Parser object with an empty self.doc
    parser = Parser()
    parser.doc = {}  # Ensure `doc` is set to an empty dictionary
    
    # Act: Call the compile method
    result = parser.compile()
    
    # Assert: The output should be a single newline since `doc` is empty
    assert result == "\n"