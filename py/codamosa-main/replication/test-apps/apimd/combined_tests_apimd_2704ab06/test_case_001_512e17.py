import pytest
from unittest.mock import MagicMock, patch
from apimd.parser import <PERSON><PERSON><PERSON>

def test_compile_generates_documentation_with_toc():
    # Setup
    parser = Parser()
    parser.toc = True
    parser.doc = {
        "module1": "# Module `module1`\n\n",
        "module1.func1": "## module1.func1()\n\n*Full name:* `module1.func1`\n\n"
    }
    parser.docstring = {
        "module1": "Module-level docstring.",
        "module1.func1": "Function-level docstring."
    }
    parser.imp = {"module1": set()}
    parser.root = {"module1": "module1", "module1.func1": "module1"}
    parser.level = {"module1": 0, "module1.func1": 1}
    
    # Mock dependencies to properly handle formatting and checks
    with patch.object(Parser, "is_public", return_value=True), \
         patch.object(Parser, "_Parser__get_const", return_value=""), \
         patch("apimd.parser.code", lambda x: f"`{x}`"), \
         patch("apimd.parser.is_magic", return_value=False):
        # Execute
        result = parser.compile()
    
    # Verify
    expected = (
        "**Table of contents:**\n"
        "+ [`module1`](#module1)\n"
        "    + [`module1.func1`](#module1-func1)\n\n"
        "# Module `module1`\n\n"
        "Module-level docstring.\n\n"
        "## module1.func1()\n\n"
        "*Full name:* `module1.func1`\n\n"
        "Function-level docstring.\n"
    )
    assert result.strip() == expected.strip()