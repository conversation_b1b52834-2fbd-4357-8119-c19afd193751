import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON>r

def test_resolve_simple_expression():
    """
    Test that `resolve` method correctly resolves global names in a simple annotation.
    """
    # Arrange
    parser = Parser()
    root = "module"
    node = MagicMock()  # Mocking the expr object
    self_ty = ""
    
    # Mock the `Resolver` behavior to simulate visiting and resolving an expression
    mocked_resolver = MagicMock()
    mocked_resolver.generic_visit.return_value = node  # Assume it processes the input node
    mocked_resolver.visit.return_value = node
    with pytest.MonkeyPatch().context() as mp:
        mp.setattr("apimd.parser.Resolver", lambda *args, **kwargs: mocked_resolver)
        mp.setattr("apimd.parser.unparse", lambda n: "resolved_annotation")  # Mocked unparse result

        # Act
        resolved_result = parser.resolve(root=root, node=node, self_ty=self_ty)

    # Assert
    assert resolved_result == "resolved_annotation"