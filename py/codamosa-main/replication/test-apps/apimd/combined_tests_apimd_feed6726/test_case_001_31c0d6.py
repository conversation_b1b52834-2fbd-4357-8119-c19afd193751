import pytest
from unittest.mock import MagicMock
from apimd.parser import Parser
from collections import namedtuple

# Mocking dependencies
arg = namedtuple('arg', ['arg', 'annotation'])

@pytest.fixture
def setup_parser():
    # Fixture to instantiate the Parser
    parser = Parser()
    parser.resolve = MagicMock(side_effect=lambda root, annotation, self_ty="": f"Resolved({annotation})")
    return parser

def test_func_ann_valid_annotations_no_self_no_cls(setup_parser):
    # Arrange
    parser = setup_parser
    root = "root_package"
    args = [
        arg(arg="x", annotation="int"),
        arg(arg="y", annotation="str"),
        arg(arg="z", annotation="list[int]"),
    ]
    has_self = False
    cls_method = False

    # Act
    result = list(parser.func_ann(root, args, has_self=has_self, cls_method=cls_method))

    # Assert
    expected_result = [
        "Resolved(int)",
        "Resolved(str)",
        "Resolved(list[int])"
    ]
    assert result == expected_result