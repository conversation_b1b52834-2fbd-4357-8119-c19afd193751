import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import Parser

@pytest.fixture
def setup_parser():
    # Fixture to create a Parser instance
    return Parser()

def test_func_ann_single_argument_no_annotation(setup_parser):
    parser = setup_parser
    # Mock dependencies
    parser.resolve = MagicMock(return_value="Any")  # Mock resolve method
    
    # Input setup
    root = "test_root"
    args = [MagicMock(arg="arg1", annotation=None)]  # Single argument with no annotation
    has_self = False
    cls_method = False

    # Expected output
    expected_output = ["Any"]

    # Execute the function under test
    result = list(parser.func_ann(root, args, has_self=has_self, cls_method=cls_method))

    # Assert the results
    assert result == expected_output
    parser.resolve.assert_not_called()  # resolve should not be called for unannotated arguments