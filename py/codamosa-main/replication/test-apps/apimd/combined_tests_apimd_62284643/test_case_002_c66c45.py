import pytest
import os
from unittest import mock
from apimd.loader import _write

def test_write_empty_string_creates_empty_file(tmp_path):
    # Arrange: Create a temporary file path
    test_file_path = tmp_path / "empty_file.txt"
    
    # Act: Call the _write method with an empty string
    _write(str(test_file_path), "")
    
    # Assert: The file is created and remains empty
    assert test_file_path.exists(), "File was not created"
    assert test_file_path.read_text(encoding="utf-8") == "", "File content is not empty"