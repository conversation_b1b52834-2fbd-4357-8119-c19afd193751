import pytest
from unittest.mock import mock_open, patch
from apimd.loader import _write

def test_write_method_writes_text_to_file():
    # Arrange
    mock_path = "test_file.txt"
    mock_content = "This is a test string."

    # Act & Assert
    with patch("builtins.open", mock_open()) as mocked_file:
        _write(mock_path, mock_content)
        mocked_file.assert_called_once_with(mock_path, "w+", encoding="utf-8")
        mocked_file().write.assert_called_once_with(mock_content)