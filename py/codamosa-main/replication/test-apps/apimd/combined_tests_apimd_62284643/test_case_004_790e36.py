import os
import pytest
from unittest.mock import mock_open, patch

def test_write_overwrites_existing_file():
    # Prepare mock file path and content
    file_path = "existing_file.txt"
    existing_content = "Old content."
    new_content = "New content."

    # Mock the open function to simulate file behavior
    m = mock_open()
    with patch("builtins.open", m):
        # Call the method under test
        from apimd.loader import _write
        _write(file_path, new_content)

        # Assert that the file was opened in write-plus mode with UTF-8 encoding
        m.assert_called_once_with(file_path, 'w+', encoding='utf-8')

        # Assert that the new content was written to the file
        m().write.assert_called_once_with(new_content)