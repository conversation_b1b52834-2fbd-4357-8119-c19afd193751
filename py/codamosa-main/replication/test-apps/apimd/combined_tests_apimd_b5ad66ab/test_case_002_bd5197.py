import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import gen_api

@pytest.fixture
def mock_logger():
    with patch("apimd.loader.logger") as mock_logger:
        yield mock_logger

@pytest.fixture
def mock_loader():
    with patch("apimd.loader.loader", return_value="Generated API content") as mock_loader:
        yield mock_loader

@pytest.fixture
def mock_isdir():
    with patch("apimd.loader.isdir", return_value=True) as mock_isdir:  # Simulate directory already existing
        yield mock_isdir

@pytest.fixture
def mock_mkdir():
    with patch("apimd.loader.mkdir") as mock_mkdir:
        yield mock_mkdir

def test_gen_api_dry_run(mock_logger, mock_loader, mock_isdir, mock_mkdir):
    # Test input
    root_names = {"Example Title": "example_module"}
    dry = True  # dry run - should not write to disk
  
    # Invoke the method under test
    result = gen_api(root_names, dry=dry)

    # Assertions
    assert len(result) == 1  # One document should be generated
    assert "Generated API content" in result[0]

    # Verify logger calls
    mock_logger.info.assert_any_call("Load root: example_module (Example Title)")  # Should log loading root
    mock_logger.info.assert_any_call("=" * 12)  # Should start dry run logging
    mock_logger.info.assert_any_call(result[0])  # Should log the generated content

    # Verify that no actual directory creation was performed
    mock_mkdir.assert_not_called()