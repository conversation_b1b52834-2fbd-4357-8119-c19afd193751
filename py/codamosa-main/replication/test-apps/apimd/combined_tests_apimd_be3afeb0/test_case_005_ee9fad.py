import pytest
from unittest.mock import patch
from apimd.loader import _read

def test_read_invalid_type():
    # Test purpose: Handle the case where the provided input path is not a string
    invalid_inputs = [123, None, [], {}, 3.14]

    for invalid_input in invalid_inputs:
        with patch("builtins.open", side_effect=TypeError("Invalid path type")):
            # Expect a TypeError when open() is called with invalid input types
            with pytest.raises(TypeError):
                _read(invalid_input)