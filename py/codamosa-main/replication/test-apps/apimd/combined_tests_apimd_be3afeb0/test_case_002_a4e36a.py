import pytest
from unittest.mock import mock_open, patch
from apimd.loader import _read  # Importing the standalone _read function.

def test_read_empty_file():
    # Define the mock path and the expected result for an empty file
    mock_path = "empty_file.txt"
    mock_file_content = ""  # Empty file content
    expected_result = ""

    # Use mock_open to simulate opening an empty file
    with patch("builtins.open", mock_open(read_data=mock_file_content)) as mock_file:
        # Call the _read function
        result = _read(mock_path)
        
        # Ensure open was called with the correct path and mode
        mock_file.assert_called_once_with(mock_path, "r")
        
        # Assert that the result is an empty string as expected
        assert result == expected_result