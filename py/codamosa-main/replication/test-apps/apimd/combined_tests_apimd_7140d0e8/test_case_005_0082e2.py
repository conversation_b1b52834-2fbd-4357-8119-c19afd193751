import pytest
from unittest.mock import patch, MagicMock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_names_cmp_lowercase_key():
    # Mock the Parser object and its attributes
    parser = Parser()
    parser.level = {"examplekey": 4}  # Mock level dictionary

    # Input string: entirely lowercase key present in the level dictionary
    input_str = "examplekey"
    
    # Call the method
    result = parser._Parser__names_cmp(input_str)
    
    # Expected output: tuple with correct mapping from level, lowercase string, and False for islower check
    expected_output = (4, "examplekey", False)

    # Assert the result
    assert result == expected_output