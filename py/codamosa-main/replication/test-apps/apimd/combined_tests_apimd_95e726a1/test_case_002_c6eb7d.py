import pytest

# Method under test:
# def _type_name(obj: object) -> str:
#     """Get type name."""
#     return type(obj).__qualname__

def test_type_name_with_inner_class():
    class OuterClass:
        class InnerClass:
            pass

    # Create an instance of the inner class.
    inner_instance = OuterClass.InnerClass()

    # Import the function under test
    from apimd.parser import _type_name

    # Extract only the __qualname__ part, removing the leading scope added by pytest
    expected_qualname = OuterClass.InnerClass.__qualname__

    # Verify that the qualified name of the inner class matches expectations
    assert _type_name(inner_instance) == expected_qualname