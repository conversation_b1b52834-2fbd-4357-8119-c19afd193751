import pytest
import ast
from unittest.mock import patch
from apimd.parser import const_type

def test_const_type_with_heterogeneous_list():
    # Create an AST node for a list with heterogeneous elements: [1, "string"]
    heterogeneous_list = ast.List(
        elts=[
            ast.Constant(value=1),       # Integer element
            ast.Constant(value="string") # String element
        ],
        ctx=ast.Load()
    )
    
    # Mock the _type_name and _e_type functions to isolate the test
    with patch('apimd.parser._type_name', side_effect=lambda x: 'list' if isinstance(x, ast.List) else type(x).__name__.lower()), \
         patch('apimd.parser._e_type', side_effect=lambda elts: '[int, str]'):  # Mock _e_type to simulate the combined type information
        
        # Call the method under test
        result = const_type(heterogeneous_list)
        
        # Assertion to validate output is as expected
        assert result == 'list[int, str]', f"Unexpected result: {result}"