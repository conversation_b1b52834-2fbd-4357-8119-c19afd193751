import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON>r

def test_is_immediate_family_invalid_input_type():
    # Create an instance of the Parser class
    parser = Parser()
    parser.root = {"key": "prefix"}  # Assign a mock root dictionary

    # Define invalid inputs for n1 and n2
    invalid_inputs = [None, 123, 12.34, [], {}, True]  # Non-string test cases

    # Test invalid n1 and n2 separately
    for invalid_input in invalid_inputs:
        # Expect AttributeError or TypeError for invalid `n1`
        with pytest.raises(AttributeError):  # Specific expectation based on error message
            parser._Parser__is_immediate_family(invalid_input, "key")
        
        # Expect AttributeError or TypeError for invalid `n2`
        with pytest.raises(AttributeError):
            parser._Parser__is_immediate_family("key", invalid_input)