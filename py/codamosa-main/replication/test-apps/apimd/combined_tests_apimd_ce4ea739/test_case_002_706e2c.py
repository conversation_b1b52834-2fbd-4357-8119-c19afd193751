import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_is_immediate_family_missing_n2_mapping():
    # Arrange: Create an instance of Parse<PERSON> with a mocked root dictionary
    parser = Parser()
    parser.root = {}  # Ensure root has no mapping for n2
    n1 = "node1"
    n2 = "node2"

    # Act & Assert: Expect a KeyError when n2 is not found in root
    with pytest.raises(KeyError):
        parser._Parser__is_immediate_family(n1, n2)