import pytest
from unittest.mock import MagicMock
from ast import Assign, Name, Constant
from apimd.parser import <PERSON>rser


def test_globals_single_target_no_type_comment():
    # Initialize the Parser instance
    parser = Parser()
    
    # Mock the root and the assignment node
    root = 'test_module'
    node = Assign(
        targets=[Name(id='MY_CONSTANT', ctx=None)], 
        value=Constant(value=42), 
        type_comment=None
    )

    # Make sure alias, root, and const dictionaries are empty
    parser.alias = {}
    parser.root = {}
    parser.const = {}
    
    # Call the globals method
    parser.globals(root, node)
    
    # Assertions for alias
    expected_name = f"{root}.MY_CONSTANT"
    assert parser.alias[expected_name] == "42"
    
    # Assertions for root and const for the constant
    assert expected_name in parser.root
    assert parser.root[expected_name] == root
    assert parser.const[expected_name] == 'int'  # Assuming `const_type` resolves 42 to 'int'