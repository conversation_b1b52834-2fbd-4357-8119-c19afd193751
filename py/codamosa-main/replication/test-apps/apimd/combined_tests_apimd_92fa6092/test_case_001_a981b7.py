import pytest
from unittest.mock import Mock
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import AnnAssign, Name, Constant

def test_globals_with_annassign():
    # Prepare a mock AnnAssign node
    node = Mock(spec=AnnAssign)

    # Set up the node's attributes
    target = Mock(spec=Name)
    target.id = "MY_CONSTANT"
    node.target = target

    value = Mock(spec=Constant)
    value.value = "42"  # Correctly mocking the 'value' attribute
    node.value = value

    annotation = Mock()  # Annotation mock
    node.annotation = annotation

    # Create an instance of Parser
    parser = Parser()

    # Mock the 'resolve' method to return a sample annotation
    parser.resolve = Mock(return_value="int")

    # Mock the unparse function to simulate its actual behavior
    from unittest.mock import patch
    with patch("apimd.parser.unparse", return_value="'42'"):
        # Call the globals method
        parser.globals("my_module", node)

    # Verify alias was updated with the expression
    assert parser.alias["my_module.MY_CONSTANT"] == "'42'"

    # Verify constants and root were properly updated
    assert parser.root["my_module.MY_CONSTANT"] == "my_module"
    assert parser.const["my_module.MY_CONSTANT"] == "int"