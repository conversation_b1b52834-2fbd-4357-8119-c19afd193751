import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON><PERSON><PERSON>


def test_globals_ignore_non_tuple_or_list_all():
    # Arrange
    parser = Parser()
    root = "test_module"
    node = MagicMock()
    node.targets = [MagicMock(id="__all__")]
    node.value = MagicMock()
    node.value.elts = None  # Simulate __all__ being neither a list nor a tuple
    node.type_comment = None
    
    parser.imp[root] = set()  # Ensure `imp` is a dictionary

    # Act
    parser.globals(root, node)

    # Assert
    assert parser.imp[root] == set(), "__all__ was not ignored as expected"