import pytest
from unittest.mock import Mock, create_autospec
from types import ModuleType
from apimd.parser import Parser

def test_load_docstring_with_empty_doc():
    # Arrange: Create a Parser instance and set up dependencies
    parser = Parser()
    parser.doc = {}  # Empty 'doc' dictionary
    parser.docstring = {}  # Ensure 'docstring' is empty before test
    mock_module = create_autospec(ModuleType, instance=True)  # Mocked module

    # Act: Call the method under test
    parser.load_docstring(root="test_root", m=mock_module)

    # Assert: Verify no changes occurred to 'docstring'
    assert parser.docstring == {}  # Expect 'docstring' to remain unchanged