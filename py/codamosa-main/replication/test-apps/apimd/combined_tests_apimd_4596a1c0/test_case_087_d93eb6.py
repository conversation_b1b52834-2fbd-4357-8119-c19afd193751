import pytest

class DummyObject:
    pass

def test__attr_invalid_single_level_attribute():
    """
    Test Purpose:
    Verify that the function _attr returns `None` for an invalid single-level attribute.
    """
    # Setup: Create an object without the specified attribute
    obj = DummyObject()  # A plain object with no custom attributes
    attr_name = "nonexistent"  # Invalid attribute name

    # Import the method under test
    from apimd.parser import _attr

    # Act
    result = _attr(obj, attr_name)

    # Assert
    assert result is None, "Expected None when accessing an invalid single-level attribute"