from unittest.mock import MagicMock, create_autospec
import pytest
from apimd.parser import Parser
from ast import Ann<PERSON><PERSON>, Assign, Delete, Name, Load


@pytest.fixture
def setup_parser():
    """
    Fixture to set up the Parser object with required mocking.
    """
    parser = Parser()
    parser.resolve = MagicMock(side_effect=lambda root, node: f"resolved_{node.id if hasattr(node, 'id') else node}")
    parser.doc = {name: "" for name in ['TestClass']}  # Simulated docs for classes
    return parser


@pytest.fixture
def mock_table(monkeypatch):
    """
    Fixture to mock the table function used in class_api.
    """
    def mock_table(*titles, items):
        title_row = " | ".join(titles)
        delimiter_row = "|:---:" * len(titles)
        item_rows = "\n".join(" | ".join(item) for item in items)
        return f"{title_row}\n{delimiter_row}\n{item_rows}\n\n"

    monkeypatch.setattr("apimd.parser.table", mock_table)


def test_class_api_large_body_deletions(setup_parser, mock_table):
    """
    Test the class_api method with a large body focusing on deletions.
    """
    parser = setup_parser

    root, name = "test_root", "TestClass"
    bases, body = [], []

    # Generate a body with attributes and deletions
    for i in range(5):
        ann_assign = create_autospec(AnnAssign, instance=True)
        ann_assign.target = Name(id=f"attr_{i}", ctx=Load())
        ann_assign.annotation = f"Annotation_{i}"
        body.append(ann_assign)

    for i in range(3):
        delete_node = create_autospec(Delete, instance=True)
        delete_node.targets = [Name(id=f"attr_{i}", ctx=Load())]
        body.append(delete_node)

    # Populate the parser.doc simulation
    parser.doc[name] = ""

    # Call the method under test
    parser.class_api(root, name, bases, body)

    # Extract documented attributes after deletions
    documented_members = parser.doc[name]

    # Verify attributes through mem logic and check deletions were handled
    assert all(
        f"attr_{i}" not in documented_members
        for i in range(3)
    ), "Some deleted attributes were not removed as expected."
    assert all(
        f"attr_{i}" in documented_members
        for i in range(3, 5)
    ), "Remaining attributes were unexpectedly deleted."