import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import Import, alias

def test_imports_with_multiple_aliases():
    # Mock `_m` and parent functions if used in the `imports` method
    def mock_m(root, name, *args):
        return f"{root}.{name}" if not args else f"{root}.{'.'.join(args)}"

    # Initialize parser instance
    parser = Parser()
    
    # Mock data
    root = "root_module"
    mock_alias1 = alias(name="module1", asname="mod1")
    mock_alias2 = alias(name="module2", asname=None)
    mock_alias3 = alias(name="module3", asname="mod3")
    node = Import(names=[mock_alias1, mock_alias2, mock_alias3])
    
    # Replace `_m` method with the mocked implementation
    parser._m = mock_m

    # Invoke the method under test
    parser.imports(root, node)
    
    # Expected alias values
    expected_alias = {
        "root_module.mod1": "module1",
        "root_module.module2": "module2",
        "root_module.mod3": "module3"
    }
    
    # Assert the alias dictionary
    assert parser.alias == expected_alias