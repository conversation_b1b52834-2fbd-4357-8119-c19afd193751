import pytest
from unittest.mock import <PERSON><PERSON>ock
from ast import Attribute

from apimd.parser import Resolver  # Import adjusted based on the package structure.

def test_visit_attribute_node_value_not_name():
    # Arrange
    node = MagicMock(spec=Attribute)
    node.value = MagicMock(spec=int)  # Mocking a `value` that is not an instance of `Name`.

    resolver = Resolver(root="root", alias={}, self_ty="Self")

    # Act
    result = resolver.visit_Attribute(node)

    # Assert
    assert result is node  # The node should be returned unmodified.