import pytest
from apimd.parser import table

def test_table_with_valid_input():
    """
    Test the `table` function with multiple column titles and valid row data.
    """
    # Given input
    titles = ("Name", "Age", "City")
    items = [
        ["<PERSON>", "30", "New York"],
        ["<PERSON>", "25", "Los Angeles"],
        ["<PERSON>", "35", "Chicago"]
    ]
    
    # Corrected expected markdown table based on `_table_split` logic
    expected_output = (
        "| Name | Age | City |\n"
        "|:----:|:---:|:----:|\n"
        "| Alice | 30 | New York |\n"
        "| Bob | 25 | Los Angeles |\n"
        "| Charlie | 35 | Chicago |\n\n"
    )
    
    # Run the function
    result = table(*titles, items=items)
    
    # Assert the result matches the expected output
    assert result == expected_output