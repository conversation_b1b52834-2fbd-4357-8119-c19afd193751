import pytest
from unittest.mock import patch
from ast import Assign, Name, Constant, Expr
from apimd.parser import Parser, const_type


@pytest.mark.parametrize(
    "node, expected_alias, should_modify",
    [
        # Valid Assign node with a single target and no type_comment
        (
            Assign(
                targets=[Name(id="MY_CONSTANT", ctx=None)],
                value=Constant(value=42),
                type_comment=None,
            ),
            {"expected_name": "test_module.MY_CONSTANT", "expected_value": "42", "expected_type": "int"},
            True,
        ),
        # Assign node with no targets - should not modify attributes
        (
            Assign(targets=[], value=Constant(value=42), type_comment=None),
            None,
            False,
        ),
        # Assign node with invalid target type - should not modify attributes
        (
            Assign(
                targets=[Expr(value=Constant(value="INVALID_TARGET"))],
                value=Constant(value=42),
                type_comment=None,
            ),
            None,
            False,
        ),
    ],
)
@patch("apimd.parser.const_type", return_value="int")
def test_globals_improvements(mock_const_type, node, expected_alias, should_modify):
    # Setup Parser instance
    parser = Parser()
    root = "test_module"
    parser.alias = {}
    parser.root = {}
    parser.const = {}

    # Capture the initial state of parser attributes
    initial_alias = parser.alias.copy()
    initial_root = parser.root.copy()
    initial_const = parser.const.copy()

    # Call the globals method
    parser.globals(root, node)

    if should_modify:
        # Assert expected alias
        expected_name = expected_alias["expected_name"]
        assert parser.alias[expected_name] == expected_alias["expected_value"]

        # Assert root and const mappings
        assert parser.root[expected_name] == root
        assert parser.const[expected_name] == expected_alias["expected_type"]

        # Ensure const_type was called as expected
        mock_const_type.assert_called_once_with(node.value)
    else:
        # Verify that parser attributes remain unchanged
        assert parser.alias == initial_alias
        assert parser.root == initial_root
        assert parser.const == initial_const