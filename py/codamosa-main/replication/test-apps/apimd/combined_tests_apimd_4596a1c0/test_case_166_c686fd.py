import pytest
from apimd.loader import walk_packages
import os

def test_walk_packages_invalid_path_yields_nothing():
    """
    Test walk_packages with an invalid path that does not exist.
    Expectation: The method should yield nothing.
    """
    invalid_path = "/non/existent/directory"
    name = "invalid_name"
    
    # Execute and assert that no packages are yielded
    result = list(walk_packages(name, invalid_path))
    assert result == []