import pytest
from unittest.mock import patch, MagicMock
from apimd.loader import gen_api

def test_gen_api_with_valid_input():
    # Arrange
    root_names = {"TestTitle": "test_module"}
    expected_markdown = [
        "# TestTitle API\n\nMocked API Documentation"
    ]
    
    # Mock dependencies
    with patch("apimd.loader.loader") as mock_loader, \
         patch("apimd.loader._site_path") as mock_site_path, \
         patch("apimd.loader._write") as mock_write, \
         patch("os.path.isdir") as mock_isdir, \
         patch("os.mkdir") as mock_mkdir, \
         patch("apimd.loader.logger") as mock_logger:
        
        mock_loader.return_value = "Mocked API Documentation"
        mock_site_path.return_value = "/mocked/site/path"
        mock_isdir.return_value = True  # Simulate that the directory exists
        
        # Act
        result = gen_api(root_names)
        
        # Assert
        assert result == expected_markdown
        mock_loader.assert_called_once_with("test_module", "/mocked/site/path", True, 1, False)
        mock_logger.info.assert_any_call("Load root: test_module (TestTitle)")
        mock_logger.info.assert_any_call("Write file: docs/test-module-api.md")