import pytest
from unittest.mock import <PERSON><PERSON>ock, patch
from types import ModuleType
from apimd.parser import Parse<PERSON>

def test_load_docstring_root_key_skipping():
    # Mocked module and attributes
    mocked_module = MagicMock(spec=ModuleType)
    matched_attr = MagicMock(__doc__="Mocked docstring")
    unmatched_attr = MagicMock(__doc__=None)  # No docstring for unmatched keys
    
    def mocked_attr(obj, attr):
        # Simulate behavior of _attr: return matched_attr only for specific keys
        if attr == "matched_key":
            return matched_attr
        return unmatched_attr
    
    # Patch `_attr` to use the simulated behavior
    with patch("apimd.parser._attr", side_effect=mocked_attr):
        # Instantiate the Parser object and setup initial conditions
        parser = Parser()
        parser.doc = {
            "root.matched_key": "Some doc content",
            "root.unmatched_key": "Should be skipped",
            "other_key": "Should also be skipped"
        }
        parser.docstring = {}
        
        # Call the method under test
        parser.load_docstring("root", mocked_module)
        
        # Verify the docstring updates only for valid keys
        assert "root.matched_key" in parser.docstring
        assert parser.docstring["root.matched_key"] == "Mocked docstring"
        assert "root.unmatched_key" not in parser.docstring
        assert "other_key" not in parser.docstring