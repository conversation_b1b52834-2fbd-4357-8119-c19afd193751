import pytest
from unittest.mock import MagicMock, patch
from apimd.parser import <PERSON>rser

@pytest.fixture
def parser():
    parser = Parser()
    parser.imp = {
        "module": {"module.func1", "module.func2"},
    }
    parser.root = {
        "module.func1": "module",
        "module.func2": "module",
    }
    parser.__all__ = {"module.func1", "module.func2"}  # Adding explicit '__all__'
    return parser

@patch("apimd.parser.is_public_family")
def test_is_public_with_valid_public_name(mock_is_public_family, parser):
    # Setup
    mock_is_public_family.return_value = False  # Simulating fallback to __all__
    parser.doc = {}
    parser.const = {}
    s = "module.func1"

    # Action
    result = parser.is_public(s)

    # Pre-assertion State Verification
    assert "module.func1" in parser.imp["module"]  # Check it exists in 'imp'
    assert parser.root["module.func1"] == "module"  # Validate 'root' mapping
    assert "module.func1" in parser.__all__  # Ensure it's in '__all__'

    # Assertion
    assert result is True  # Should be considered public
    mock_is_public_family.assert_not_called()  # Should not call external check