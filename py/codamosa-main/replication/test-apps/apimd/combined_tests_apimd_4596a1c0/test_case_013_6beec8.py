import pytest
from unittest.mock import MagicMock
from ast import Subscript, Name, Constant, BinOp, BitOr, Load
from apimd.parser import Resolver

def test_visit_subscript_typing_optional_transformation():
    # Mock setup
    alias_mock = {"typing.Optional": "typing.Optional"}
    resolver = Resolver(root="", alias=alias_mock)
    
    # Input AST Node
    node = Subscript(
        value=Name(id="typing.Optional", ctx=Load()), 
        slice=Name(id="int", ctx=Load()), 
        ctx=Load()
    )

    # Expected Output AST Node
    expected = BinOp(
        left=Name(id="int", ctx=Load()), 
        op=BitOr(), 
        right=Constant(value=None)
    )

    # Call the method
    result = resolver.visit_Subscript(node)

    # Verify result
    assert isinstance(result, BinOp)
    assert isinstance(result.op, BitOr)
    assert isinstance(result.right, Constant)
    assert result.right.value is None
    assert isinstance(result.left, Name)
    assert result.left.id == "int"