import pytest
from apimd.parser import Parser

def test_new_creates_instance_with_valid_inputs():
    # Arrange: Define input parameters
    link = True
    level = 2
    toc = False

    # Act: Call the `new` class method
    instance = Parser.new(link=link, level=level, toc=toc)

    # Assert: Verify the created instance has the correct attributes
    assert isinstance(instance, Parser)  # Should be an instance of Parser
    assert instance.link == link         # Attribute `link` matches input
    assert instance.b_level == level     # Attribute `b_level` matches input level
    assert instance.toc == toc           # Attribute `toc` matches input