import pytest
from unittest.mock import create_autospec
from apimd.parser import Parser
from ast import Assign, Tuple, Constant, Name, Store

def test_globals_process_all():
    # Arrange: Create the Parser object
    parser = Parser()
    root = "test_module"
    parser.imp[root] = set()  # Ensure the root key exists in imp

    # Create a mock AST node for __all__ = ("item1", "item2")
    node = Assign()
    node.targets = [Name(id="__all__", ctx=Store())]
    node.value = Tuple(elts=[Constant(value="item1"), Constant(value="item2")], ctx=None)

    # Act: Call the `globals` method
    parser.globals(root, node)

    # Assert: Verify that the imp dictionary has been updated correctly
    assert parser.imp[root] == {"test_module.item1", "test_module.item2"}