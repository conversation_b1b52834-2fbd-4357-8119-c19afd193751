import pytest
from apimd.parser import Resolver  # Adjusted import statement

def test_resolver_init_empty_root():
    # Arrange
    root = ""  # Edge case: Empty string for root
    alias = {"exampleAlias": "example.Value"}  # Non-empty dictionary
    self_ty = "SelfType"  # Non-empty string

    # Act
    resolver = Resolver(root=root, alias=alias, self_ty=self_ty)

    # Assert
    assert resolver.root == ""  # Ensure root is initialized correctly
    assert resolver.alias == {"exampleAlias": "example.Value"}  # Ensure alias is set correctly
    assert resolver.self_ty == "SelfType"  # Ensure self_ty is set correctly