import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import FunctionDef


def test_api_method_no_docstring():
    # Arrange
    parser = Parser()
    root = "test_module"
    parser.doc = {root: "Documentation placeholder"}
    parser.level = {root: 0}
    parser.root = {root: root}
    parser.docstring = {}
    parser.imp = {root: set()}

    # Mock a FunctionDef node with no docstring
    node = MagicMock(spec=FunctionDef)
    node.name = "test_function"
    node.decorator_list = []
    node.args = MagicMock()
    node.returns = None
    node.body = []
    node.bases = []

    # Mock 'get_docstring' to simulate no docstring
    mocked_get_docstring = MagicMock(return_value=None)
    global get_docstring
    get_docstring = mocked_get_docstring

    # Act
    parser.api(root=root, node=node, prefix="test_prefix")

    # Assert
    full_name = "test_module.test_prefix.test_function"
    assert full_name in parser.doc
    assert parser.docstring == {}
    assert "class" not in parser.doc[full_name]  # Verifies it is not a class doc