import pytest
from apimd.parser import esc_underscore

def test_esc_underscore_multiple_underscores():
    # Input string contains more than one underscore
    input_string = "hello_world_example"
    expected_output = "hello\\_world\\_example"
    
    # Call the method under test
    result = esc_underscore(input_string)
    
    # Assert the output is as expected
    assert result == expected_output, f"Expected '{expected_output}', but got '{result}'"