import pytest
from unittest.mock import patch
from apimd.parser import Parser


def test_is_public_not_in_imp():
    # Arrange
    with patch("apimd.parser.is_public_family", side_effect=lambda s: s == "test_string") as mock_is_public_family:
        parser = Parser()

        # Configure `imp` and `root` such that the test scenario works without key errors
        parser.imp = {"test_root": {"test_string"}}  # `test_string` is part of `__all__`.
        parser.root = {"test_string": "test_root"}  # Maps `test_string` to its root "test_root".

        test_string = "test_string"

        # Act
        result = parser.is_public(test_string)

        # Assert
        mock_is_public_family.assert_not_called()  # Should not call `is_public_family` if found in `__all__`.
        assert result is True  # Validate that `test_string` is public.

    # Additional scenario: If `is_public_family` gets called and returns False
    with patch("apimd.parser.is_public_family", return_value=False) as mock_is_public_family_false:
        parser = Parser()

        # Simulate a scenario where `test_string` is not part of `__all__`.
        parser.imp = {"test_root": set()}
        parser.root = {"test_string": "test_root"}  # Valid mapping for `root`.

        result = parser.is_public(test_string)

        # Assert
        mock_is_public_family_false.assert_called_once_with(test_string)  # Fallback check for `is_public_family`.
        assert result is False  # Confirm that `test_string` is not public without `__all__`.