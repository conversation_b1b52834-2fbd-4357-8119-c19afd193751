import pytest
from unittest.mock import patch, MagicMock
from apimd.parser import <PERSON>sol<PERSON>
from ast import Name, Load, Expr


def test_visit_name_alias_resolves_to_expression_with_context_validation():
    # Arrange
    alias_mapping = {
        "AliasName": "ResolvedExpression"
    }
    resolver = Resolver(root="root", alias=alias_mapping, self_ty="")
    
    # Create mock objects for parsed expression and intermediate resolution steps
    mock_expr = MagicMock(spec=Expr)  # Mocked parsed AST expression
    mock_expr_value = MagicMock()  # Value field of the parsed expression
    mock_expr.value = mock_expr_value  # Assign to `value` of the mocked expression
    
    alias_name = "AliasName"  # An alias in `self.alias` that should resolve
    resolved_raw_expression = "ResolvedExpression"  # Expected raw expression for alias

    # Patch `parse`, the `visit` method, and `_m`
    with patch("apimd.parser.parse", return_value=MagicMock(body=[mock_expr])) as mock_parse, \
         patch.object(resolver, "visit", return_value="ProcessedNode") as mock_visit, \
         patch("apimd.parser._m", return_value=alias_name) as mock_m:
        
        # Create an AST node mimicking the alias
        node = Name(id=alias_name, ctx=Load())

        # Act
        result = resolver.visit_Name(node)

        # Assert
        # Ensure `_m` was called to retrieve the alias name
        mock_m.assert_called_once_with("root", alias_name)
        
        # Validate the alias mapping resolution triggered a parse call
        mock_parse.assert_called_once_with(resolved_raw_expression)
        
        # Ensure the recursive `visit` handler processed the expected value
        mock_visit.assert_called_once_with(mock_expr_value)
        
        # Validate the final result matches the expected recursive visit output
        assert result == "ProcessedNode"

        # Validate the context of the output remains `Load`
        assert isinstance(node.ctx, Load), "The context of the node should remain Load"