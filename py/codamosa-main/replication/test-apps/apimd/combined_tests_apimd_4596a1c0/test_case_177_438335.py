import pytest
from unittest.mock import patch, MagicMock
import sys
from apimd.loader import gen_api

@patch('apimd.loader._site_path', return_value="/mock/site-packages/sample_module")  # Mock _site_path
@patch('apimd.loader.logger')  # Mock the logger to prevent real logging during tests
@patch('apimd.loader._write')  # Mock the _write function to prevent file writes
@patch('apimd.loader.loader')  # Mock the loader function for API content generation
@patch('apimd.loader.isdir', return_value=False)  # Assume directory does not exist
@patch('apimd.loader.mkdir')  # Mock the mkdir function to prevent actual directory creation
def test_gen_api_with_pwd(mock_mkdir, mock_isdir, mock_loader, mock_write, mock_logger, mock_site_path):
    # Arrange
    root_names = {"SampleTitle": "sample_module"}
    pwd_path = "/mock/path"
    mock_loader.return_value = "Generated Markdown Content"

    # Act
    with patch.object(sys, 'path', new=[]):  # Ensure sys.path starts clean for this test
        result = gen_api(root_names, pwd=pwd_path, prefix="mock_docs", dry=True)

    # Assert
    # Verify pwd was appended to sys.path
    assert pwd_path in sys.path

    # Verify directory creation was triggered
    mock_mkdir.assert_called_once_with("mock_docs")

    # Verify loader function was called with correct arguments
    mock_loader.assert_called_once_with(
        "sample_module", 
        "/mock/site-packages/sample_module",  # Mocked _site_path result
        True, 
        1, 
        False
    )

    # Verify no actual file was written since dry=True
    mock_write.assert_not_called()

    # Verify Markdown content in the result
    assert result == ["# SampleTitle API\n\nGenerated Markdown Content"]