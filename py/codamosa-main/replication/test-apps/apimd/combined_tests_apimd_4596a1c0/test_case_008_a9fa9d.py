import pytest
from unittest.mock import patch, MagicMock
from apimd.parser import Resolver
from ast import Name, Load, Expr, parse


def test_visit_name_alias_resolves_to_expression():
    # Arrange
    alias_mapping = {
        "AliasName": "ResolvedExpression"
    }
    resolver = Resolver(root="root", alias=alias_mapping, self_ty="")
    # Mock `parse` to return a mock expression
    mock_expr = MagicMock(spec=Expr)
    mock_expr_value = MagicMock()  # the value of the parsed expression
    mock_expr.value = mock_expr_value

    # Patch `parse` globally and `_m` locally
    with patch("apimd.parser.parse", return_value=MagicMock(body=[mock_expr])):
        alias_name = "AliasName"
        with patch.object(resolver, "visit", return_value="ProcessedNode") as mock_visit, \
             patch("apimd.parser._m", return_value=alias_name):
            node = Name(id=alias_name, ctx=Load())

            # Act
            result = resolver.visit_Name(node)

            # Assert
            mock_visit.assert_called_once_with(mock_expr_value)
            assert result == "ProcessedNode"