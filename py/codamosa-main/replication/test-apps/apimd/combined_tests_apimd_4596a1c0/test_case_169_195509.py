import pytest
from unittest.mock import Mock, patch
from apimd.loader import _load_module
from importlib.machinery import ModuleSpec
from importlib.abc import Loader

def test_load_module_with_invalid_loader():
    # Mocking inputs
    name = "example_module"
    path = "/path/to/example_module.py"
    parser_mock = Mock()

    # Mocking the spec_from_file_location to return a spec with a loader not of type Loader
    spec_mock = Mock(spec=ModuleSpec)
    spec_mock.loader = object()  # Invalid loader type (not an instance of Loader)

    with patch("apimd.loader.spec_from_file_location", return_value=spec_mock):
        with patch("apimd.loader.parent", return_value="parent_module"):
            with patch("builtins.__import__") as import_mock:
                # Run the method under test
                result = _load_module(name, path, parser_mock)

                # Assert that ImportError was never raised
                import_mock.assert_called_once_with("parent_module")
                
                # Assert the method returned False due to invalid loader type
                assert result is False