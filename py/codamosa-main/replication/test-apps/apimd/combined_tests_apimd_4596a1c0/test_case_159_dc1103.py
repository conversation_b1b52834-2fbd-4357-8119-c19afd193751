import pytest
from unittest import mock
import os

from apimd.loader import _write


def test_write_large_string():
    # Mocked path for writing
    mocked_path = "/mocked/test_file.txt"
    
    # A very large string (e.g., 10 million characters)
    large_string = "a" * 10_000_000

    # Mock open to avoid actual file system I/O
    with mock.patch("builtins.open", mock.mock_open()) as mocked_file:
        _write(mocked_path, large_string)
        
        # Check that the file was opened with the correct parameters
        mocked_file.assert_called_once_with(mocked_path, 'w+', encoding='utf-8')
        
        # Check that the write operation was performed with the large string
        mocked_file().write.assert_called_once_with(large_string)