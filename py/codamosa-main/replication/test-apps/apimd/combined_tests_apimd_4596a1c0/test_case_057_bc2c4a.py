import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON>rser

def test_is_immediate_family_empty_strings():
    # Set up the Parser instance
    parser = Parser()
    parser.root = {"" : ""}  # Initialize self.root with an entry for the empty string
    
    # Inputs
    n1 = ""
    n2 = ""

    # Execute the method under test
    result = parser._Parser__is_immediate_family(n1, n2)

    # Assert the expected output
    assert result is True, "Empty strings should be considered as immediate family based on the current implementation"