import pytest
from unittest.mock import Mock
from ast import Name, Load
from apimd.parser import Resolver

def test_visit_Name_self_ty_match():
    # Mock inputs
    mock_alias = {}
    resolver = Resolver(root="root_module", alias=mock_alias, self_ty="expected_id")
    input_node = Name(id="expected_id", ctx=Load())
    
    # Call the method under test
    result = resolver.visit_Name(input_node)
    
    # Verify the result
    assert isinstance(result, Name)
    assert result.id == "Self"
    assert isinstance(result.ctx, Load)