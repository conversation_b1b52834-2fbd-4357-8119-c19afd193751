import pytest
from unittest.mock import patch, call
from apimd.loader import gen_api

def test_gen_api_dry_run():
    # Test input
    root_names = {"Example Title": "example_module"}
    dry = True  # Dry run - should not write to disk
    
    with patch("apimd.loader.logger") as mock_logger, \
         patch("apimd.loader.loader", return_value="Generated API content") as mock_loader, \
         patch("apimd.loader.isdir", return_value=True) as mock_isdir, \
         patch("apimd.loader.mkdir") as mock_mkdir, \
         patch("apimd.loader._write") as mock_write:  # Mock the file writing method.
        
        # Invoke the method under test
        result = gen_api(root_names, dry=dry)

        # Assertions on the result
        assert len(result) == 1  # One document should be generated
        assert result[0].startswith("# Example Title API\n\n")  # Check the generated content structure
        assert "Generated API content" in result[0]  # Ensure the key content is included

        # Verify logger calls in the correct sequence
        mock_logger.info.assert_has_calls([
            call("Load root: example_module (Example Title)"),  # Log for loading root
            call("Write file: docs/example-module-api.md"),  # Simulated file write path
            call("=" * 12),  # Logging dry run separator
            call(result[0])  # Log the generated content
        ], any_order=False)

        # Verify that no directory creation or file writing was performed
        mock_mkdir.assert_not_called()
        mock_write.assert_not_called()