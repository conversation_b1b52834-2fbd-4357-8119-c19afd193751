import pytest
from apimd.parser import _table_split

def test_table_split_with_varying_string_lengths():
    # Define the input list of strings with varying lengths
    input_args = ['a', 'abcd', 'xy']
    
    # Expected output string based on the rules
    expected_output = '|:---:|:----:|:---:|'
    
    # Call the _table_split method with the input arguments
    result = _table_split(input_args)
    
    # Assert that the result matches the expected output
    assert result == expected_output