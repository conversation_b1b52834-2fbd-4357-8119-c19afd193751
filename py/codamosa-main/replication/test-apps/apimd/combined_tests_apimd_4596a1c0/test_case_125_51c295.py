import pytest
from apimd.parser import _table_cell

def test_table_cell_single_item():
    # Input: An iterable containing a single string
    items = ["singleItem"]
    
    # Expected output
    expected_output = "| singleItem |"
    
    # Actual output
    actual_output = _table_cell(items)
    
    # Assertion
    assert actual_output == expected_output, f"Expected {expected_output} but got {actual_output}"