import pytest
import ast
from unittest.mock import patch
from apimd.parser import const_type

def test_const_type_varied_lists():
    # Test case 1: Heterogeneous list with integers and strings
    heterogeneous_list = ast.List(
        elts=[
            ast.Constant(value=1),       # Integer element
            ast.Constant(value="string") # String element
        ],
        ctx=ast.Load()
    )

    # Test case 2: Empty list
    empty_list = ast.List(
        elts=[],
        ctx=ast.Load()
    )
    
    # Test case 3: Nested list structure
    nested_list = ast.List(
        elts=[
            ast.List(elts=[ast.Constant(value=1), ast.Constant(value=2)], ctx=ast.Load()),  # Inner list with integers
            ast.Constant(value="outer string")  # Outer string element
        ],
        ctx=ast.Load()
    )

    # Test case 4: Larger list with repeated elements for performance
    large_list = ast.List(
        elts=[ast.Constant(value=i) for i in range(1000)],  # List with 1000 integers
        ctx=ast.Load()
    )

    # Mock the _type_name and _e_type functions to isolate the test
    def mock_e_type(*elts):
        if not elts or not elts[0]:
            return "[]"
        elif all(isinstance(e, ast.Constant) and isinstance(e.value, int) for e in elts[0]):
            return "[int]"
        elif len(elts[0]) == 2 and isinstance(elts[0][0], ast.List) and isinstance(elts[0][1], ast.Constant):
            return "[nested structure, str]"
        elif isinstance(elts[0][0], ast.List):
            return "[nested structure]"
        return "[int, str]"

    with patch('apimd.parser._type_name', side_effect=lambda x: 'list' if isinstance(x, ast.List) else type(x).__name__.lower()), \
         patch('apimd.parser._e_type', side_effect=mock_e_type):
        
        # Test case 1: Heterogeneous list
        result = const_type(heterogeneous_list)
        assert result == 'list[int, str]', f"Unexpected result for heterogeneous list: {result}"
        
        # Test case 2: Empty list
        result = const_type(empty_list)
        assert result == 'list[]', f"Unexpected result for empty list: {result}"
        
        # Test case 3: Nested list
        result = const_type(nested_list)
        assert result == 'list[nested structure, str]', f"Unexpected result for nested list: {result}"
        
        # Test case 4: Larger list
        result = const_type(large_list)
        assert result == 'list[int]', f"Unexpected result for large list: {result}"