import pytest
from ast import Constant
from apimd.parser import const_type
import sys

def test_const_type_with_various_constant_nodes():
    # Test with an integer
    int_node = Constant(value=42)
    int_result = const_type(int_node)
    assert int_result == "int", "Expected result for int node is 'int'"

    # Test with a string
    string_node = Constant(value="hello")
    string_result = const_type(string_node)
    assert string_result == "str", "Expected result for string node is 'str'"

    # Test with a boolean
    bool_node = Constant(value=True)
    bool_result = const_type(bool_node)
    assert bool_result == "bool", "Expected result for boolean node is 'bool'"

    # Test with a float
    float_node = Constant(value=3.14)
    float_result = const_type(float_node)
    assert float_result == "float", "Expected result for float node is 'float'"

    # Test with an empty string
    empty_string_node = Constant(value="")
    empty_string_result = const_type(empty_string_node)
    assert empty_string_result == "str", "Expected result for empty string node is 'str'"

    # Test with a very large float
    large_float_node = Constant(value=sys.float_info.max)
    large_float_result = const_type(large_float_node)
    assert large_float_result == "float", f"Expected result for large float node is 'float' (value: {sys.float_info.max})"