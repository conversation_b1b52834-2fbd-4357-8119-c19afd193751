import pytest
from unittest.mock import Mock
from ast import Subscript, Name, Load
from apimd.parser import Resolver  # Adjusted import to align with module path setup

def test_visit_Subscript_non_tuple_slice_for_typing_union():
    # Mock alias mapping to resolve 'typing.Union'
    alias_mock = {'typing.Union': 'typing.Union'}

    # Instantiate the Resolver with mock alias
    resolver = Resolver(root="", alias=alias_mock)

    # Create a Subscript node where value is 'Name' with id='typing.Union'
    # and slice is not a Tuple
    subscript_node = Subscript(
        value=Name(id='typing.Union', ctx=Load()),
        slice=Mock(),  # Mocked slice to simulate it is not a Tuple
        ctx=Load()
    )

    # Call the method
    result = resolver.visit_Subscript(subscript_node)

    # Assert that the slice itself is returned
    assert result == subscript_node.slice