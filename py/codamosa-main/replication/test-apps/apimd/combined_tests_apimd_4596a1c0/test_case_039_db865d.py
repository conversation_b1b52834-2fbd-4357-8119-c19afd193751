import pytest
from unittest.mock import Magic<PERSON>ock, patch
from apimd.parser import Parser

@pytest.fixture
def parser_instance():
    # Creating a Parser instance
    return Parser()

def test_func_api_empty_args_and_returns(parser_instance):
    # Mocking required components within the Parser class
    mock_node = MagicMock()
    mock_node.posonlyargs = []
    mock_node.args = []
    mock_node.kwonlyargs = []
    mock_node.defaults = []
    mock_node.vararg = None
    mock_node.kw_defaults = []
    mock_node.kwarg = None
    
    mock_func_ann = MagicMock(return_value=[])
    mock_table = MagicMock()

    # Inject mock dependencies
    with patch.object(parser_instance, 'func_ann', mock_func_ann), \
         patch('apimd.parser.table', mock_table):
        
        # Mocking `doc` structure
        parser_instance.doc = {"test_func": ""}

        # Method under test
        parser_instance.func_api(
            root="test_root",
            name="test_func",
            node=mock_node,
            returns=None,
            has_self=False,
            cls_method=False
        )

        # Assertions
        assert parser_instance.doc["test_func"] != ""
        mock_func_ann.assert_called_once()
        mock_table.assert_called_once()