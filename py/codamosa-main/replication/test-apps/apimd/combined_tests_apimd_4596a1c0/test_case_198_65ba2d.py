import ast
import pytest
from apimd.parser import const_type

def test_const_type_with_varied_tuples():
    # Test homogeneous tuple of integers
    tuple_node_ints = ast.Tuple(elts=[ast.Constant(value=1), ast.Constant(value=2), ast.Constant(value=3)])
    result_ints = const_type(tuple_node_ints)
    assert result_ints == 'tuple[int]', f"Expected 'tuple[int]' but got {result_ints}"
    
    # Test tuple with mixed types
    tuple_node_mixed = ast.Tuple(elts=[ast.Constant(value=1), ast.Constant(value='a'), ast.Constant(value=3.14)])
    result_mixed = const_type(tuple_node_mixed)
    assert result_mixed == 'tuple[Any]', f"Expected 'tuple[Any]' but got {result_mixed}"
    
    # Test empty tuple
    tuple_node_empty = ast.<PERSON><PERSON>(elts=[])
    result_empty = const_type(tuple_node_empty)
    assert result_empty == 'tuple', f"Expected 'tuple' but got {result_empty}"