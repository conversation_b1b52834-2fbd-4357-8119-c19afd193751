import pytest
from unittest.mock import MagicMock, patch
from ast import AnnAs<PERSON>, Name, Assign, Delete, Constant, AST
from apimd.parser import Parser

@pytest.fixture
def parser():
    # Set up a fresh Parser instance for each test
    return Parser()

@pytest.fixture
def mock_ast_nodes():
    # Create mock AST nodes for the test
    ann_assign = AnnAssign(
        target=Name(id="attribute_to_keep", ctx=None),
        annotation=Name(id="int", ctx=None),
        value=None,
        simple=1
    )
    assign = Assign(
        targets=[Name(id="attribute_to_delete", ctx=None)],
        value=Constant(value=42)
    )
    delete = Delete(targets=[Name(id="attribute_to_delete", ctx=None)])
    return [ann_assign, assign, delete]

def test_class_api_with_deletions(parser, mock_ast_nodes):
    # Prepare test inputs
    root = "test_module"
    class_name = "TestClass"
    bases = []
    body = mock_ast_nodes

    # Mock out methods used within class_api
    parser.resolve = MagicMock(side_effect=lambda _root, _node: str(_node.id))
    parser.doc = {class_name: ""}

    # Call the function under test
    parser.class_api(root, class_name, bases, body)

    # Assertions
    assert "attribute_to_keep" in parser.doc[class_name]
    assert "attribute_to_delete" not in parser.doc[class_name]