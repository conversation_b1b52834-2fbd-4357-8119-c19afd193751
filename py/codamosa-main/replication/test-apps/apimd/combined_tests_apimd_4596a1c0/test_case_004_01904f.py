import pytest
from unittest.mock import Mock
from ast import Constant, Expr, parse, dump
from apimd.parser import Resolver  # Ensure the module structure matches the actual project.

def test_visit_constant_parsable_string():
    # Setup
    resolver = Resolver(root="root_module", alias={}, self_ty="self_type")
    mock_node = Constant(value="2 + 2")
    
    # Mock the visit method to ensure it gets called
    resolver.visit = Mock(return_value="Processed Result")
    
    # Invoke the method under test
    result = resolver.visit_Constant(mock_node)
    
    # Get the parsed expression for comparison
    parsed_expr = parse("2 + 2").body[0].value
    
    # Assert that the visit method was called with an equivalent AST
    # Compare using ast.dump for structural equality
    assert len(resolver.visit.call_args_list) == 1
    called_arg = resolver.visit.call_args_list[0][0][0]
    assert dump(called_arg) == dump(parsed_expr)
    
    # Assert the correct output
    assert result == "Processed Result"