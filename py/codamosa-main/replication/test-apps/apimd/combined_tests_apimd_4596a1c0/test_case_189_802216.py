import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>  # Properly import the Parser class

def test_find_alias_when_not_immediate_family():
    """
    Test __find_alias method to ensure proper behavior when an alias fails
    the __is_immediate_family condition.
    """
    # Setup
    parser = Parser()  # Instantiate the Parser class
    parser.alias = {"alias_key": "alias_value"}  # alias exists in the alias dictionary
    parser.doc = {"alias_value.some_key": "test documentation"}  # alias exists in doc
    parser.docstring = {"alias_value.some_key": "test docstring"}
    parser.root = {"alias_value.some_key": "alias_value"}
    parser.level = {"alias_value.some_key": 2}
    parser.const = {"alias_value.some_key": "const_value"}

    # Mock the __is_immediate_family private method to simulate failure
    parser._Parser__is_immediate_family = MagicMock(return_value=False)

    # Ensure alias value is in doc to pass the first condition of the loop
    parser.doc["alias_value"] = "alias documentation"

    # Call the actual method
    parser._Parser__find_alias()

    # Assertions to validate that no changes occurred due to alias failing immediate family check
    assert parser.doc == {
        "alias_value.some_key": "test documentation",
        "alias_value": "alias documentation",
    }
    assert parser.docstring == {"alias_value.some_key": "test docstring"}
    assert parser.root == {"alias_value.some_key": "alias_value"}
    assert parser.level == {"alias_value.some_key": 2}
    assert parser.const == {"alias_value.some_key": "const_value"}

    # Verify that __is_immediate_family was called with the expected arguments
    parser._Parser__is_immediate_family.assert_called_once_with("alias_key", "alias_value")