import pytest
from apimd.parser import Parser

def test_func_ann_with_empty_args():
    # Arrange
    parser = Parser()
    root = "test_root"
    args = []  # Empty sequence for args
    has_self = False
    cls_method = False
    
    # Act
    result = list(parser.func_ann(root, args, has_self=has_self, cls_method=cls_method))
    
    # Assert
    assert result == [], "Expected empty sequence for result when args are empty."