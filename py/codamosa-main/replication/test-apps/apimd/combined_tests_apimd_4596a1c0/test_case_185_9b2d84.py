import pytest
from unittest.mock import <PERSON><PERSON>ock, patch
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import FunctionDef

def test_api_function_node_no_prefix_no_decorators_improved():
    # Arrange
    parser = Parser()  # Initialize the Parser instance
    root = "test_root"
    
    # Manually setting up the `parser` object state
    parser.level[root] = 0  # Assuming root level starts at 0
    parser.root[root] = root  # Set the root for the test case

    # Mocking a FunctionDef node
    function_node = MagicMock(spec=FunctionDef)
    function_node.name = "example_function"
    function_node.decorator_list = []  # Empty decorator list
    function_node.args = MagicMock()  # Placeholder for function arguments
    function_node.body = []  # Function body (empty for this test)
    function_node.returns = None  # No return annotation
    
    # Mocking dependencies
    with patch.object(parser, 'func_api') as mock_func_api, \
         patch.object(parser, 'resolve', side_effect=lambda x, y=None: x) as mock_resolve:
        
        # Act
        parser.api(root, function_node, prefix="")

        # Assert
        full_name = f'{root}.example_function'
        
        # Assertions for `parser.doc` content
        assert full_name in parser.doc, "The function documentation should be created in parser.doc."
        assert parser.doc[full_name].startswith('#' * (parser.b_level + 2) + " example_function()"), \
            "The generated documentation should have the correct header level and function name."
        assert "@" not in parser.doc[full_name], \
            "The generated documentation should not include any decorators for a function without decorators."
        
        # Assertions for `parser.level` and `parser.root`
        assert parser.level[full_name] == parser.level[root], \
            "The level of the function node should be the same as the root level."
        assert parser.root[full_name] == root, \
            "The root of the function node should be set correctly."

        # Verify `func_api` is called once with the expected arguments
        mock_func_api.assert_called_once_with(
            root, 
            full_name, 
            function_node.args, 
            function_node.returns,
            has_self=False,
            cls_method=False
        )

        # No direct call to resolve is expected for the node name
        mock_resolve.assert_not_called()