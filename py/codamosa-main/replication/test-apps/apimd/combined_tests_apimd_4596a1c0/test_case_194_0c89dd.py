import pytest
from apimd.parser import Parser

def test_is_public_with_invalid_input_types():
    # Arrange
    parser = Parser()
    # Initializing empty dictionaries to prevent KeyError due to missing attributes
    parser.imp = {}
    parser.root = {}

    # Define test inputs and expected exception for invalid types
    invalid_types = [123, 45.67, None, [], {}, set()]

    for invalid_input in invalid_types:
        with pytest.raises((KeyError, TypeError)):
            # Act - Test for non-string invalid inputs
            parser.is_public(invalid_input)

    # Act & Assert
    valid_but_missing_key = "non_existent_key"
    with pytest.raises(KeyError):
        # Test valid string input that does not exist in self.root
        parser.is_public(valid_but_missing_key)