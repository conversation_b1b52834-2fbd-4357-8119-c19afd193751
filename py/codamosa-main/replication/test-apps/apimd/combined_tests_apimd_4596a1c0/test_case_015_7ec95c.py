import pytest
from unittest.mock import Mock
from ast import Attribute, Name, Load
from apimd.parser import Resolver


def test_visit_attribute_typing_prefix():
    # Create a mock AST node for testing
    mock_node = Mock(spec=Attribute)
    mock_node.value = Mock(spec=Name)
    mock_node.value.id = "typing"
    mock_node.attr = "List"

    # Instantiate the Resolver class
    resolver = Resolver(root="", alias={})

    # Call the method under test
    result = resolver.visit_Attribute(mock_node)

    # Verify that the result is of type Name and has the expected attributes
    assert isinstance(result, Name)
    assert result.id == mock_node.attr
    assert isinstance(result.ctx, Load)