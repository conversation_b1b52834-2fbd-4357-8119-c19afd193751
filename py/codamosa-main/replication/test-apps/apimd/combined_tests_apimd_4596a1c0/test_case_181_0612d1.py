import pytest
from unittest.mock import Mock, patch, create_autospec
from apimd.parser import Parser

def test_imports_with_node_module_none():
    # Arrange
    parser = Parser()
    initial_alias = parser.alias.copy()  # To verify the alias remains unchanged
    root = "sample_root"

    # Mocking a node object with module=None and names attribute
    node = Mock()
    node.module = None
    # Ensuring `name` and `asname` attributes exist on each mock object in names
    name1 = Mock()
    name1.name = "TestName1"
    name1.asname = None

    name2 = Mock()
    name2.name = "TestName2"
    name2.asname = "AliasName2"

    node.names = [name1, name2]

    # Mocking the '_m' function correctly at the module level
    with patch("apimd.parser._m", return_value=None) as mock_m:
        # Act
        parser.imports(root, node)

        # Assert
        # Check '_m' was not called since `module` is None
        mock_m.assert_not_called()

        # Verify the alias dictionary remains unchanged
        assert parser.alias == initial_alias, "The alias dictionary should remain unchanged when module is None."

        # Validate that all names in the 'names' attribute were correctly accessed
        accessed_names = [entry.name for entry in node.names]
        assert accessed_names == ["TestName1", "TestName2"], "All names should have been processed correctly."