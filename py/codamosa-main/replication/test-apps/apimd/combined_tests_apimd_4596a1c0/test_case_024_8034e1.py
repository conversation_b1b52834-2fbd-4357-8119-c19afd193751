import pytest
from apimd.parser import Parser

def test_post_init_toc_is_falsy():
    # Case where toc is falsy and link should retain its default True value
    parser = Parser()
    parser.toc = False  # Setting 'toc' to a falsy value
    
    # Invoking __post_init__
    parser.__post_init__()

    # Assert that 'link' retains its default value (True by class definition)
    assert parser.link is True