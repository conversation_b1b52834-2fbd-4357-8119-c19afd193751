import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON>rse<PERSON>, ANY  # Import the ANY constant directly
from collections import namedtuple

@pytest.fixture
def setup_parser():
    # Fixture to create a Parser instance
    return Parser()

def test_func_ann_single_argument_no_annotation(setup_parser):
    parser = setup_parser
    # Mock dependencies
    parser.resolve = MagicMock()  # resolve is mocked, but should not be called in this test

    # Input setup
    root = "test_root"
    Arg = namedtuple("Arg", ["arg", "annotation"])  # Using a namedtuple for consistency
    args = [Arg(arg="arg1", annotation=None)]  # Single argument with no annotation
    has_self = False
    cls_method = False

    # Expected output
    expected_output = [ANY]  # Use the actual value from the apimd.parser module

    # Execute the function under test
    result = list(parser.func_ann(root, args, has_self=has_self, cls_method=cls_method))

    # Assert the results
    assert result == expected_output
    parser.resolve.assert_not_called()  # resolve should not be called for unannotated arguments