import pytest
from unittest.mock import Mock, patch
from apimd.parser import Parser

@pytest.fixture
def parser_with_data():
    """Fixture for the Parser instance with mocked data."""
    parser = Parser()
    parser.imp = {'some_key': set()}  # Mock data for `self.imp`
    parser.doc = {}  # Ensure `self.doc` has no keys starting with `s.`
    parser.const = {}  # Ensure `self.const` has no keys starting with `s.`
    parser.root = {'some_key': 'some_key'}  # Mock data for `self.root`
    return parser

@patch('apimd.parser.is_public_family', return_value=False)
def test_is_public_no_matching_key(mock_is_public_family, parser_with_data):
    """
    Test case for the is_public method:
    - Ensures it returns False when `s` exists in `self.imp`, but no keys starting with `s.` exist in `self.doc` or `self.const`.
    """
    parser = parser_with_data
    result = parser.is_public('some_key')
    
    # Assertion for the result being False
    assert result is False
    
    # Ensure is_public_family is called for completeness
    mock_is_public_family.assert_not_called()