import pytest
from apimd.parser import _type_name

def test_type_name_with_none():
    # Input: None
    input_value = None
    
    # Expected Output: 'NoneType', since type(None).__qualname__ == 'NoneType'
    expected_output = "NoneType"
    
    # Invoke the method
    result = _type_name(input_value)
    
    # Assertion
    assert result == expected_output, f"Expected '{expected_output}', but got '{result}'"