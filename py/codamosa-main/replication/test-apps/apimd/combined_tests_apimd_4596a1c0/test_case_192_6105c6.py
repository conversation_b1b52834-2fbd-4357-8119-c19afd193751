import pytest
from unittest.mock import Mock, patch
from apimd.parser import Parser

@pytest.fixture
def parser_with_mocked_dependencies():
    # Create an instance of <PERSON><PERSON><PERSON> with mocked dependencies
    parser = Parser()
    # Simulated `self.imp` with an empty __all__ list for `root_name`, and example non-empty lists 
    parser.imp = {"root_name": [], "other_name": ["value1", "value2"]}
    # Simulated `self.root` mapping
    parser.root = {"test_name": "root_name", "other_test_name": "other_name"}
    # Simulated `self.doc`
    parser.doc = {"test_name": "documentation", "other_test_name": "documentation"}
    # Simulated `self.const`
    parser.const = {"test_name": "constant_value", "other_test_name": "constant_value"}
    return parser

@patch("apimd.parser.is_public_family")
def test_is_public_extended_case(mock_is_public_family, parser_with_mocked_dependencies):
    # Configure the mock for is_public_family
    mock_is_public_family.return_value = True

    # Input string `s`
    s = "test_name"

    # Call the method under test
    result = parser_with_mocked_dependencies.is_public(s)

    # Assert that is_public_family was called with the correct argument
    mock_is_public_family.assert_called_once_with(s)

    # Verify the result is delegated to is_public_family
    assert result == True

    # Additional assertions for other attributes in `self.imp`, `self.root`, `self.doc`, and `self.const`
    # Ensure no unexpected influence from other mocked values
    assert parser_with_mocked_dependencies.imp["other_name"] == ["value1", "value2"]
    assert parser_with_mocked_dependencies.doc["test_name"] == "documentation"
    assert parser_with_mocked_dependencies.const["test_name"] == "constant_value"

    # Verify that all mocked mappings were accessed correctly (if applicable)
    assert "test_name" in parser_with_mocked_dependencies.root
    assert "test_name" in parser_with_mocked_dependencies.doc
    assert "test_name" in parser_with_mocked_dependencies.const