import pytest
from unittest.mock import patch
from apimd.parser import _e_type
import ast  # Python's Abstract Syntax Tree module

def test_e_type_with_heterogeneous_sequences():
    # Create test inputs using Python's ast.Constant
    constant_int = ast.Constant(value=42)
    constant_str = ast.Constant(value="test")
    
    # Sequence with mixed types (int and str)
    sequence_with_mixed_types = [[constant_int, constant_str]]

    # Mock the _type_name function to return appropriate type names
    def mock_type_name(obj):
        if isinstance(obj, int):
            return "int"
        elif isinstance(obj, str):
            return "str"
        return "Unknown"

    with patch('apimd.parser._type_name', side_effect=mock_type_name):
        # Call the actual method under test
        result = _e_type(*sequence_with_mixed_types)
        
        # Validate
        assert result == "[Any]", f"Expected '[Any]', but got '{result}'"