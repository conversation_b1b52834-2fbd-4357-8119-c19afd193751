import pytest
from unittest.mock import MagicMock, patch
from ast import ClassDef, Assign, Name, Constant

# Mock for walk_body since it is likely an imported utility in Parser and not defined in the provided code
def mock_walk_body(nodes):
    return nodes

# Test case for the `class_api` method
def test_class_api_handles_enum_correctly():
    # Import the Parser class and any utilities from the respective module
    from apimd.parser import Parser

    # Arrange: Create the Parser instance and mocks
    parser = Parser()
    parser.resolve = MagicMock(side_effect=lambda root, name: name)  # Simplified resolve mock
    parser.doc = {}
    
    class_name = "MyEnumClass"
    root = "module"
    bases = [MagicMock(value="enum.Enum")]  # Mock a base class from enum
    body = [
        Assign(targets=[Name(id="RED", ctx=None)], value=Constant(value=1)),
        Assign(targets=[Name(id="GREEN", ctx=None)], value=Constant(value=2)),
        Assign(targets=[Name(id="BLUE", ctx=None)], value=Constant(value=3))
    ]
    
    # Ensure the doc entry for the class is initialized
    parser.doc[class_name] = ""
    
    with patch("apimd.parser.walk_body", side_effect=mock_walk_body):
        # Act: Call the class_api method
        parser.class_api(root, class_name, bases, body)
    
    # Assert: Ensure only "Enums" table is created
    assert class_name in parser.doc
    assert "Enums" in parser.doc[class_name]
    assert "Members" not in parser.doc[class_name]
    assert all(color in parser.doc[class_name] for color in ["RED", "GREEN", "BLUE"])