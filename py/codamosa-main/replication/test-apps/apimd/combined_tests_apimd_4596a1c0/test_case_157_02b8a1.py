import pytest
from unittest.mock import patch, mock_open
from apimd.loader import _write

def test_write_invalid_path_raises_exception():
    # Arrange
    invalid_path = "/invalid/path/to/file.txt"  # An inaccessible or invalid path
    content = "Sample text content"

    # Mock open to raise an OSError when trying to access the invalid path
    with patch("builtins.open", side_effect=OSError("Invalid path or insufficient permissions")):
        # Act and Assert
        with pytest.raises(OSError, match="Invalid path or insufficient permissions"):
            _write(invalid_path, content)