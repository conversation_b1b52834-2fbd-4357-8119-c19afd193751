import pytest
from unittest.mock import Mock, patch
from types import ModuleType
from apimd.parser import Parser

@pytest.fixture
def parser_instance():
    """Fixture to provide a Parser instance with predefined doc."""
    parser = Parser()
    parser.doc = {
        "root.funcA": "Some doc structure for funcA",
        "root.funcB": "Other doc structure for funcB",
        "unrelated.funcC": "Should not be processed",
    }
    parser.docstring = {}
    return parser

def test_load_docstring(parser_instance):
    """Test load_docstring with valid root matching keys and module attributes."""
    mock_module = Mock(spec=ModuleType)
    
    # Mock attributes and their return values for getdoc
    def mock_getdoc(attr):
        if attr == mock_module.funcA:
            return "Docstring for funcA"
        elif attr == mock_module.funcB:
            return "Docstring for funcB"
        return None
    
    # Define module attributes
    mock_module.funcA = Mock()
    mock_module.funcB = Mock()
    
    with patch("apimd.parser.getdoc", side_effect=mock_getdoc), \
         patch("apimd.parser._attr", side_effect=lambda m, attr: getattr(m, attr)), \
         patch("apimd.parser.doctest", side_effect=lambda doc: f"Processed({doc})"):
        
        parser_instance.load_docstring("root", mock_module)

    # Validate the docstring is populated correctly
    assert parser_instance.docstring == {
        "root.funcA": "Processed(Docstring for funcA)",
        "root.funcB": "Processed(Docstring for funcB)"
    }