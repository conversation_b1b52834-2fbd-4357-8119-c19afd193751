import pytest
import ast
from unittest.mock import patch
from apimd.parser import const_type

# Mock the behavior of _e_type for testing
@patch("apimd.parser._e_type")
def test_const_type_various_dict_cases(mock_e_type):
    # Define mock behavior for _e_type, including edge cases and nested types
    def mock_e_type_side_effect(keys, values):
        if keys is None and values is None:  # Empty dictionary
            return "[None, None]"
        key_types = set(_type_name(k.value) for k in keys if isinstance(k, ast.Constant))
        value_types = set(_type_name(v.value) for v in values if isinstance(v, ast.Constant))
        
        # Handle nested dictionaries as a special case
        if any(isinstance(v, ast.Dict) for v in values):
            value_types.add("dict")
        
        key_type_string = " | ".join(sorted(key_types)) if key_types else "None"
        value_type_string = " | ".join(sorted(value_types)) if value_types else "None"
        
        return f"[{key_type_string}, {value_type_string}]"

    mock_e_type.side_effect = mock_e_type_side_effect

    def _type_name(value):
        """Helper function to mock types based on value type."""
        if isinstance(value, str):
            return "str"
        elif isinstance(value, int):
            return "int"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, dict):
            return "dict"
        elif isinstance(value, tuple):
            return "tuple"
        else:
            return "Any"

    # Test case 1: Dictionary with string keys and mixed int/str values
    dict_node1 = ast.Dict(
        keys=[
            ast.Constant(value="key1"),  # String key
            ast.Constant(value="key2")  # Another string key
        ],
        values=[
            ast.Constant(value=42),         # Integer value
            ast.Constant(value="string")   # String value
        ]
    )
    expected_type1 = "dict[str, int | str]"
    result_type1 = const_type(dict_node1)
    assert result_type1 == expected_type1, f"Failed on dict with string keys and mixed values: {result_type1}"

    # Test case 2: Empty dictionary
    dict_node2 = ast.Dict(keys=[], values=[])
    expected_type2 = "dict[None, None]"
    result_type2 = const_type(dict_node2)
    assert result_type2 == expected_type2, f"Failed on empty dictionary: {result_type2}"

    # Test case 3: Dictionary with integer keys and integer values
    dict_node3 = ast.Dict(
        keys=[
            ast.Constant(value=1),  # Integer key
            ast.Constant(value=2)  # Another integer key
        ],
        values=[
            ast.Constant(value=100),  # Integer value
            ast.Constant(value=200)  # Another integer value
        ]
    )
    expected_type3 = "dict[int, int]"
    result_type3 = const_type(dict_node3)
    assert result_type3 == expected_type3, f"Failed on dict with int keys and int values: {result_type3}"

    # Test case 4: Dictionary with non-string keys (e.g., tuple) and string values
    dict_node4 = ast.Dict(
        keys=[
            ast.Tuple(elts=[ast.Constant(value=1), ast.Constant(value=2)]),  # Tuple key
            ast.Tuple(elts=[ast.Constant(value=3), ast.Constant(value=4)])  # Another tuple key
        ],
        values=[
            ast.Constant(value="value1"),  # String value
            ast.Constant(value="value2")  # Another string value
        ]
    )
    expected_type4 = "dict[None, str]"  # Adjusted expectation
    result_type4 = const_type(dict_node4)
    assert result_type4 == expected_type4, f"Failed on dict with tuple keys and string values: {result_type4}"

    # Test case 5: Dictionary with nested dictionaries as values
    nested_dict_node = ast.Dict(
        keys=[
            ast.Constant(value="outer_key1"),  # String key
            ast.Constant(value="outer_key2")  # Another string key
        ],
        values=[
            ast.Dict(  # Nested dictionary
                keys=[ast.Constant(value="inner_key1")],
                values=[ast.Constant(value=1)]
            ),
            ast.Dict(  # Another nested dictionary
                keys=[ast.Constant(value="inner_key2")],
                values=[ast.Constant(value="nested_value")]
            )
        ]
    )
    expected_type5 = "dict[str, dict]"
    result_type5 = const_type(nested_dict_node)
    assert result_type5 == expected_type5, f"Failed on dict with nested dictionaries as values: {result_type5}"