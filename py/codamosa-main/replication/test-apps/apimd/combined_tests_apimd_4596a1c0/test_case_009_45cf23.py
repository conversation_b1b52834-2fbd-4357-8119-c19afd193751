import pytest
from unittest.mock import MagicMock, patch
from apimd.parser import Resolver
from ast import Name, Call, Expr, parse

@pytest.fixture
def resolver():
    """Fixture to initialize a Resolver instance."""
    return Resolver(root="module", alias={"Alias": "typing.TypeVar"}, self_ty="")

def test_visit_name_typing_typevar(resolver):
    # Mock _m function to return the alias key
    with patch("apimd.parser._m", return_value="Alias"):
        # Create a node where id corresponds to an alias resolving to 'typing.TypeVar'
        node = Name(id="Alias", ctx=MagicMock())

        # Mock parse to return an Expr containing a Call with func as a Name with id 'TypeVar'
        mock_expr = MagicMock(spec=Expr)
        mock_expr.value = MagicMock(spec=Call)
        mock_expr.value.func = MagicMock(spec=Name)
        mock_expr.value.func.id = "TypeVar"

        with patch("apimd.parser.parse", return_value=MagicMock(body=[mock_expr])):
            # Invoke the method under test
            result = resolver.visit_Name(node)

            # Assert that the original node is returned unmodified
            assert result == node