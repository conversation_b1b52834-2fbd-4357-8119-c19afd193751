import pytest
from apimd.parser import Parser
from ast import Import, alias

def test_imports_with_multiple_aliases():
    # Initialize Parser instance
    parser = Parser()
    
    # Set up input data for the test
    root = "root_module"
    mock_alias1 = alias(name="module1", asname="mod1")
    mock_alias2 = alias(name="module2", asname=None)
    mock_alias3 = alias(name="module3", asname="mod3")
    node = Import(names=[mock_alias1, mock_alias2, mock_alias3])
    
    # Invoke the actual method under test
    parser.imports(root, node)
    
    # Expected result: the alias dictionary should be updated according to the `imports` method implementation
    expected_alias = {
        "root_module.mod1": "module1",  # Asname is used for this alias
        "root_module.module2": "module2",  # No asname, so original name is used
        "root_module.mod3": "module3"   # Asname is used for this alias
    }
    
    # Assert: Check if the alias dictionary in the parser matches the expected result
    assert parser.alias == expected_alias

    # Additional assertion to validate compliance with intermediate steps
    for alias_key, module_name in expected_alias.items():
        assert alias_key.startswith(root)  # Ensure alias keys include the root module
        assert module_name in [mock_alias1.name, mock_alias2.name, mock_alias3.name]  # Ensure correct module names are processed