import pytest
from apimd.parser import _table_split

def test_table_split_long_strings():
    # Input: List of strings with lengths significantly greater than 3
    input_args = ['abcdef', 'QRSTUVWXYZ']
    
    # Based on the implementation in `_table_split`, the length of the dashes for 
    # 'QRSTUVWXYZ' will match exactly 10 (the length of 'QRSTUVWXYZ'), and it does not add any extra dashes.
    expected_output = '|:------:|:----------:|'
    
    # Call the method
    result = _table_split(input_args)

    # Assert the result matches the expected output
    assert result == expected_output, f"Expected '{expected_output}', but got '{result}'"