import pytest
from unittest.mock import MagicMock, patch
from apimd.loader import loader


@patch("apimd.loader.walk_packages")
@patch("apimd.loader.isfile")
@patch("apimd.loader._read")
@patch("apimd.loader.Parser")
def test_loader_handles_various_file_conditions(
    mock_parser, mock_read, mock_isfile, mock_walk_packages
):
    # Arrange
    root = "/path/to/root"
    pwd = "/path/to/pwd"
    link = True
    level = 1
    toc = True

    # Mock Parser instance and its methods
    parser_instance = MagicMock()
    mock_parser.new.return_value = parser_instance

    # Mock config for walk_packages: returning two modules
    mock_walk_packages.return_value = [("module1", "/path/to/module1"), ("module2", "/path/to/module2")]

    # Mock behavior for `isfile` to simulate existent and non-existent files
    def mock_isfile_side_effect(path):
        return path in [
            "/path/to/module1.py",
            "/path/to/module2.py",
            "/path/to/module2.pyi",
        ]

    mock_isfile.side_effect = mock_isfile_side_effect

    # Mock `_read` to return content, empty files, or raise an error (e.g., for unsupported paths)
    def mock_read_side_effect(path):
        if path == "/path/to/module1.py":
            return "content of /path/to/module1.py"  # Valid content
        elif path == "/path/to/module2.py":
            return "content of /path/to/module2.py"  # Valid content
        elif path == "/path/to/module2.pyi":
            return ""  # Empty (valid but no meaningful content)
        raise FileNotFoundError(f"File not found: {path}")

    mock_read.side_effect = mock_read_side_effect

    # Act
    result = loader(root, pwd, link, level, toc)

    # Assert
    # Verify that the parser instance was correctly created
    mock_parser.new.assert_called_once_with(link, level, toc)

    # Ensure `walk_packages` was called once with the root and pwd
    mock_walk_packages.assert_called_once_with(root, pwd)

    # Check calls to `parser_instance.parse`:
    expected_parse_calls = [
        ("module1", "content of /path/to/module1.py"),
        ("module2", "content of /path/to/module2.py"),
        ("module2", ""),  # The `.pyi` file is processed even if empty
    ]
    actual_parse_calls = [call.args for call in parser_instance.parse.call_args_list]
    assert actual_parse_calls == expected_parse_calls

    # Ensure `parser_instance.compile` was called exactly once and loader output matches
    parser_instance.compile.assert_called_once()
    assert result == parser_instance.compile.return_value

    # Extra: Confirm `_read` raises FileNotFoundError for unsupported paths
    with pytest.raises(FileNotFoundError, match="File not found: /path/to/unsupported.py"):
        mock_read("/path/to/unsupported.py")