import pytest
from unittest.mock import patch
from apimd.parser import _defaults

def mock_code(doc):
    # Simple mock to replicate `code` functionality for testing
    return f"`{doc}`"

def mock_unparse(node):
    # Raise an exception to simulate invalid input parsing
    raise ValueError("Invalid expression")

def test_defaults_with_invalid_input():
    invalid_input = [42, {"key": "value"}, set()]  # Non-None, non-expression values

    with patch("apimd.parser.code", side_effect=mock_code), \
         patch("apimd.parser.unparse", side_effect=mock_unparse):
        with pytest.raises(ValueError, match="Invalid expression"):
            # Consume the iterator to trigger processing
            list(_defaults(invalid_input))