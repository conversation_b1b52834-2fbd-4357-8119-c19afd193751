import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON>solver
from ast import Name

def test_visit_Name_no_alias():
    # Create an instance of the Resolver class
    resolver = Resolver(root="test_root", alias={})

    # Create a Name node with an id that has no corresponding alias
    test_node = Name(id="unknown_name", ctx=MagicMock())

    # Call the visit_Name method
    result = resolver.visit_Name(test_node)

    # Assert that the result is the same as the original node
    assert result == test_node