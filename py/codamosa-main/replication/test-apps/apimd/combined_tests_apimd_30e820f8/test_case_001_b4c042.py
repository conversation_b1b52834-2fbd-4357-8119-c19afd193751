import pytest
from unittest.mock import MagicMock
from apimd.parser import Resolver
from ast import Name, Load

def test_visit_name_replaces_self_ty_with_self():
    # Arrange
    resolver = Resolver(root='test_root', alias={}, self_ty='self_ty')
    node = Name(id='self_ty', ctx=Load())
    
    # Act
    result = resolver.visit_Name(node)

    # Assert
    assert isinstance(result, Name)
    assert result.id == "Self"