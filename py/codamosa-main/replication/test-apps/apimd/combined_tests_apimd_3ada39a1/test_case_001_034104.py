import pytest
from unittest.mock import Mock
from apimd.parser import <PERSON>solver
from ast import Name, Load

def test_visit_name_updates_to_self():
    # Arrange
    resolver = Resolver(root='module', alias={}, self_ty='self')
    node = Name(id='self', ctx=Load())

    # Act
    result = resolver.visit_Name(node)

    # Assert
    assert isinstance(result, Name)
    assert result.id == "Self"
    assert isinstance(result.ctx, Load)