import pytest
from unittest.mock import <PERSON>Mock
from apimd.parser import <PERSON>sol<PERSON>
from ast import Name, Load

def test_visit_name_with_nonexistent_id():
    # Setup for Resolver with a dummy alias and root
    resolver = Resolver(root='fake_root', alias={'existing_name': 'some_expression'})
    
    # Create a Name node with an ID not present in the alias dictionary
    name_node = Name(id='non_existent_name', ctx=Load())
    
    # Call visit_Name should return the original node unchanged
    result = resolver.visit_Name(name_node)
    
    # Assert that the result is the same as the input node
    assert result == name_node, "Expected the original node to be returned unchanged."