import pytest
from unittest.mock import MagicMock
from apimd.parser import <PERSON>sol<PERSON>
from ast import Name, Load

def test_visit_name_with_typing_typevar():
    # Setup
    alias = {
        'some_name': 'typing.TypeVar',
    }
    self_ty = ""
    resolver = Resolver(root='some_root', alias=alias, self_ty=self_ty)
    
    # Create a Name node representing the alias pointing to a TypeVar
    node = Name(id='some_name', ctx=Load())

    # Call the method under test
    result = resolver.visit_Name(node)

    # Assert: The result should be the original node unchanged
    assert result is node