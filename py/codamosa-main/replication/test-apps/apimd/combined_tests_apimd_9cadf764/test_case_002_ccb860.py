import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>

def test_get_const_no_matching_constants():
    # Arrange
    parser = Parser()
    
    # Mock the instance attributes
    parser.const = {"CONST_A": "int", "CONST_B": "str"}  # Constants that will not match
    parser.root = {"CONST_A": "root_a", "CONST_B": "root_b"}  # Roots that will not match the input 'name'
    parser.is_public = MagicMock(return_value=True)  # Assume all constants are public
    
    # Input name that does not match any constant in parser.root
    input_name = "non_matching_name"

    # Act
    result = parser._Parser__get_const(input_name)

    # Assert
    assert result == "", "Expected an empty string when no constants match the given name"