import pytest
from apimd.parser import Resolver

def test_resolver_initialization_with_empty_root():
    # Prepare the test inputs
    root = ""
    alias = {"name": "example"}
    self_ty = "self_type"

    # Create an instance of the Resolver
    resolver = Resolver(root=root, alias=alias, self_ty=self_ty)

    # Assertions to verify the object is initialized correctly
    assert resolver.root == root
    assert resolver.alias == alias
    assert resolver.self_ty == self_ty