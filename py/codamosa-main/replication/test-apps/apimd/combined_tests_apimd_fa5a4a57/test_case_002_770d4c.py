import pytest
from unittest.mock import <PERSON><PERSON>ock
from ast import Attribute, Name, Load
from apimd.parser import Resolver

def test_visit_attribute_without_typing_prefix():
    # Arrange: Create a Resolver instance and mock an Attribute node
    resolver = Resolver("root", {}, "")
    
    # Mock the Attribute node where 'node.value' is Name and 'node.value.id' is not 'typing'
    node = MagicMock(spec=Attribute)
    node.value = MagicMock(spec=Name)
    node.value.id = "not_typing"
    node.attr = "example"

    # Act: Call the method under test
    result = resolver.visit_Attribute(node)

    # Assert: The original node should be returned without modification
    assert result == node