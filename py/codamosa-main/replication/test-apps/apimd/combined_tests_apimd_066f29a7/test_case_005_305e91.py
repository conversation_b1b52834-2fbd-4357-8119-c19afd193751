import pytest
from apimd.parser import _table_cell

def test_table_cell_with_long_strings():
    # Input: Iterable with extremely long strings
    items = ['a' * 1000, 'b' * 2000]
    
    # Expected: Proper Markdown-formatted string without truncation
    expected_output = f"| {'a' * 1000} | {'b' * 2000} |"
    
    # Call the method
    result = _table_cell(items)
    
    # Assert the output matches the expected result
    assert result == expected_output