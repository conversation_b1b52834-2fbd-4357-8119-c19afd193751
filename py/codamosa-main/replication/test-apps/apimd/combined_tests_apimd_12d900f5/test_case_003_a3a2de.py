import pytest
from apimd.parser import _table_split  # Assuming _table_split is accessible for testing.

def test_table_split_all_three_characters():
    # Input: All strings have exactly 3 characters
    input_args = ['one', 'two', 'xyz']
    # Expected output
    expected_output = '|:---:|:---:|:---:|'
    
    # Call the method under test
    result = _table_split(input_args)
    
    # Assertion
    assert result == expected_output, f"Expected {expected_output}, but got {result}"