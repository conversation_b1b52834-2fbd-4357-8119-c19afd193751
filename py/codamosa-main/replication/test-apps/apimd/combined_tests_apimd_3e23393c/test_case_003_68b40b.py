import pytest
from apimd.parser import Resolver

def test_resolver_init_invalid_root_type():
    """Test exceptional case with invalid root type."""
    invalid_root = 123  # root should be a string, but we're providing an integer
    valid_alias = {'mod': 'module'}
    valid_self_ty = 'self_type'

    # Initialize the Resolver instance
    resolver = Resolver(root=invalid_root, alias=valid_alias, self_ty=valid_self_ty)

    # Check if the root is correctly set and if it's the expected type
    assert not isinstance(resolver.root, str), "Expected root to be of type str."