import pytest
from unittest.mock import Mock, patch
from apimd.parser import Parser

@pytest.fixture
def parser_with_mocked_dependencies():
    # Create an instance of Parser with mocked dependencies
    parser = Parser()
    parser.imp = {"root_name": []}  # Simulate `self.imp` with an empty __all__ list for root_name
    parser.root = {"test_name": "root_name"}  # Simulate `self.root` mapping
    return parser

@patch("apimd.parser.is_public_family")
def test_is_public_empty_all_list(mock_is_public_family, parser_with_mocked_dependencies):
    # Configure the mock for is_public_family
    mock_is_public_family.return_value = True

    # Input string `s`
    s = "test_name"

    # Call the method under test
    result = parser_with_mocked_dependencies.is_public(s)

    # Assert that is_public_family was called with the correct argument
    mock_is_public_family.assert_called_once_with(s)

    # Verify the result is delegated to is_public_family
    assert result == True