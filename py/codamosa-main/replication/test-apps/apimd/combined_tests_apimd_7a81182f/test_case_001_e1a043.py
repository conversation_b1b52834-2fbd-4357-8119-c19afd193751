import pytest
from ast import Subscript, Constant

from apimd.parser import Resolver

def test_visit_Subscript_with_non_Name_value():
    # Setup: Create an instance of Resolver
    resolver = Resolver(root="root", alias={})

    # Create a Subscript node with a value that is not an instance of Name
    non_name_value = Constant(value="not_a_name")
    node = Subscript(value=non_name_value, slice=None, ctx=None)

    # Execute: Call visit_Subscript
    result = resolver.visit_Subscript(node)

    # Assert: Expect the original node to be returned unchanged
    assert result == node