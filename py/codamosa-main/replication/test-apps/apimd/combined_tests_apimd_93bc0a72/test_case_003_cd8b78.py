import pytest
from unittest import mock
from apimd.loader import loader

def test_loader_with_extension_modules_only():
    # Mock dependencies
    with mock.patch('apimd.loader.walk_packages') as mock_walk_packages, \
         mock.patch('apimd.loader.isfile') as mock_isfile, \
         mock.patch('apimd.loader.Parser') as MockParser, \
         mock.patch('apimd.loader._load_module') as mock_load_module, \
         mock.patch('apimd.loader.EXTENSION_SUFFIXES', [".cpython-310-darwin.so", ".so", ".pyd"]):

        # Configure mocks
        # Mock walk_packages to simulate a directory with extension modules
        mock_walk_packages.return_value = [("extension_module", "/fake/path/extension_module")]

        # Simulate files that match the extension suffixes
        def isfile_side_effect(path):
            for ext in [".cpython-310-darwin.so", ".so", ".pyd"]:
                if path == f"/fake/path/extension_module{ext}":
                    return True  # Simulate the presence of these extension files
            return False
        mock_isfile.side_effect = isfile_side_effect

        # Simulate Parser and its methods
        mock_parser_instance = mock.Mock()
        MockParser.new.return_value = mock_parser_instance
        mock_parser_instance.compile.return_value = "compiled_output"

        # Simulate _load_module to load the extension module successfully
        def load_module_side_effect(name, path, parser):
            if name == "extension_module" and path.endswith(".cpython-310-darwin.so"):
                return True  # Simulate successful load of this specific extension
            return False
        mock_load_module.side_effect = load_module_side_effect

        # Execute the function
        result = loader(root="/fake/root", pwd="/fake/pwd", link=False, level=1, toc=True)

        # Assertions
        mock_walk_packages.assert_called_once_with("/fake/root", "/fake/pwd")
        mock_isfile.assert_any_call("/fake/path/extension_module.cpython-310-darwin.so")
        mock_load_module.assert_any_call(
            "extension_module", "/fake/path/extension_module.cpython-310-darwin.so", mock_parser_instance
        )
        mock_parser_instance.compile.assert_called_once()

        assert result == "compiled_output"