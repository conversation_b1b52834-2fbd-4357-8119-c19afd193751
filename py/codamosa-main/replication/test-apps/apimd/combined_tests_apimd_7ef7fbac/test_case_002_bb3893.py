import pytest
from apimd.parser import table

def test_table_with_single_column_and_row_data():
    # Test case: Single column title and single-column row data
    titles = ["Header"]
    items = ["Row1", "Row2", "Row3"]
    
    # Expected Markdown table
    expected_output = (
        "| Header |\n"
        "|:------:|\n"
        "| Row1 |\n"
        "| Row2 |\n"
        "| Row3 |\n\n"
    )
    
    # Invoke the table method
    result = table(*titles, items=items)
    
    # Verify the output matches expectations
    assert result == expected_output