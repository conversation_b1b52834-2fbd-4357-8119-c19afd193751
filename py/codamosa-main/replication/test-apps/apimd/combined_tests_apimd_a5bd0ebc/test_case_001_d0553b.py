import pytest
from unittest.mock import <PERSON><PERSON>ock
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import FunctionDef

def test_api_function_node_no_prefix_no_decorators():
    # Arrange
    parser = Parser()  # Initialize the Parser instance
    root = "test_root"
    # Manually initialize the required state
    parser.level[root] = 0  # Assuming root level starts at 0
    parser.root[root] = root  # Set the root for the test case
    
    function_node = FunctionDef(
        name="example_function",  # Function name
        args=MagicMock(),       # Placeholder for function args
        body=[],                # Function body (empty for this test)
        decorator_list=[],      # No decorators
        returns=None,           # No return annotation
        type_ignores=[]         # Placeholder for type ignores
    )
    
    # Act
    parser.api(root, function_node, prefix="")

    # Assert
    full_name = f'{root}.example_function'
    assert full_name in parser.doc, "The function documentation should be created in parser.doc."
    assert parser.doc[full_name].startswith('#' * (parser.b_level + 2) + " example_function()"), \
        "The generated documentation should have the correct header level and function name."
    assert parser.level[full_name] == parser.level[root], \
        "The level of the function node should be the same as the root level."
    assert parser.root[full_name] == root, \
        "The root of the function node should be set correctly."