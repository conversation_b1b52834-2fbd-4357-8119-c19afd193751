import pytest
from unittest.mock import Mock
from apimd.parser import <PERSON><PERSON><PERSON>
from ast import Name, Load

@pytest.fixture
def resolver_instance():
    # Create a resolver instance with a mock root and alias dictionary
    root = "root_module"
    alias = {}  # Empty alias as per the test specification
    self_ty = ""  # No special `self_ty` value for this test
    return Resolver(root=root, alias=alias, self_ty=self_ty)

def test_visit_name_returns_original_node(resolver_instance):
    # Arrange: Create a Name node whose id is not in resolver_instance.alias
    node_id = "non_alias_name"
    node = Name(id=node_id, ctx=Load())

    # Act: Call the visit_Name method
    result = resolver_instance.visit_Name(node)

    # Assert: Ensure the original node is returned as is
    assert result == node, "The original node should be returned when its id is not in alias"