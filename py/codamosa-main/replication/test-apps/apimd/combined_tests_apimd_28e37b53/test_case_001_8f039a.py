import pytest
from unittest.mock import Mock
from apimd.parser import _e_type
from ast import Constant

def mock_type_name(value):
    # Mock behavior of `_type_name` based on input value types.
    if isinstance(value, int):
        return "int"
    elif isinstance(value, str):
        return "str"
    elif isinstance(value, float):
        return "float"
    return "Unknown"

@pytest.mark.parametrize("elements, expected_result", [
    # Test a case with strings
    (([Constant(value="a"), Constant(value="b")],), "[str]"),
    # Test a case with integers
    (([Constant(value=1), Constant(value=2)],), "[int]"),
    # Test a case with mix types, which leads to "Any"
    (([Constant(value="a"), Constant(value=1)],), "[Any]"),
    # Test an empty sequence
    (([],), ""),
    # Test a sequence containing None
    (([None],), ""),
])
def test_e_type(monkeypatch, elements, expected_result):
    # Mock _type_name behavior
    monkeypatch.setattr("apimd.parser._type_name", mock_type_name)

    # Execute and assert
    assert _e_type(*elements) == expected_result