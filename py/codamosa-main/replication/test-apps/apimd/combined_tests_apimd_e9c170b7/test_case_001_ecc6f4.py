import pytest
from apimd.parser import Parser

def test_post_init_toc_truthy():
    # Setup: Create an instance of the Parser class with a truthy `toc` value
    parser = Parser()
    parser.toc = True  # Set `toc` to a truthy value
    
    # Invoke the __post_init__ method
    parser.__post_init__()
    
    # Assertion: Verify that the `link` attribute is set to True
    assert parser.link is True, "The `link` attribute should be True when `toc` is truthy."