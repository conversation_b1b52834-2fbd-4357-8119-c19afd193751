{"meta": {"software": "slipcover", "version": "1.0.16", "timestamp": "2025-06-12T16:58:09.932164", "branch_coverage": true, "show_contexts": false}, "files": {"combined_tests_dataclasses-json_445598cb/test_case_001_5aef4c.py": {"executed_lines": [1, 2, 4, 6, 9, 10, 11], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "dataclasses_json/__init__.py": {"executed_lines": [2, 5, 6], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 3, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "dataclasses_json/api.py": {"executed_lines": [1, 2, 3, 4, 7, 10, 11, 13, 14, 15, 18, 19, 20, 21, 24, 25, 26, 27, 28, 31, 32, 37, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 66, 67, 68, 69, 70, 78, 79, 80, 82, 85, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 118, 119, 130, 131, 133, 135, 138, 139, 144, 147, 148, 149, 150, 152, 154, 155], "missing_lines": [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 71, 72, 73, 74, 75, 76, 83, 86, 100, 102, 103, 104, 106, 108, 109, 110, 111, 112, 113, 114, 115, 134, 140, 141, 142], "executed_branches": [[133, 135], [139, 144]], "missing_branches": [[102, 103], [102, 108], [104, 106], [104, 108], [133, 134], [139, 140]], "summary": {"covered_lines": 72, "missing_lines": 36, "covered_branches": 2, "missing_branches": 6, "percent_covered": 63.793103448275865}}, "dataclasses_json/cfg.py": {"executed_lines": [1, 2, 4, 6, 8, 11, 12, 16, 17, 21, 23, 24, 25, 26, 41, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 58, 60, 61, 63, 64, 66, 69, 70, 71, 72, 73, 77, 79, 80, 82, 84, 85, 86, 87, 88, 89, 94, 95, 97], "missing_lines": [67, 75, 76, 90, 92], "executed_branches": [[55, 56], [55, 58], [60, 61], [60, 63], [63, 64], [63, 66], [66, 69], [69, 70], [69, 79], [70, 72], [79, 80], [79, 82], [82, 84], [82, 94], [84, 85], [85, 86], [94, 95], [94, 97]], "missing_branches": [[66, 67], [70, 75], [84, 92], [85, 90]], "summary": {"covered_lines": 50, "missing_lines": 5, "covered_branches": 18, "missing_branches": 4, "percent_covered": 88.31168831168831}}, "dataclasses_json/undefined.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 9, 11, 13, 14, 17, 18, 19, 20, 26, 27, 31, 33, 34, 38, 40, 41, 42, 44, 45, 46, 50, 51, 52, 53, 54, 55, 56, 59, 60, 65, 66, 67, 68, 69, 70, 71, 72, 73, 76, 79, 80, 86, 87, 88, 89, 90, 91, 93, 94, 95, 96, 98, 99, 101, 102, 118, 121, 122, 130, 131, 133, 134, 169, 170, 176, 177, 179, 181, 182, 183, 184, 185, 189, 191, 193, 194, 195, 196, 197, 198, 199, 200, 201, 203, 204, 205, 206, 207, 209, 210, 243, 244, 245, 246, 247, 248, 251, 256, 259, 260, 264, 265, 266, 269, 270, 273], "missing_lines": [24, 100, 103, 104, 105, 107, 108, 109, 110, 112, 113, 114, 115, 116, 135, 136, 137, 138, 140, 142, 143, 144, 145, 148, 149, 150, 151, 152, 154, 155, 156, 158, 160, 161, 162, 164, 166, 167, 211, 212, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 226, 227, 228, 229, 231, 232, 233, 234, 235, 236, 237, 238, 239, 241, 249, 250, 252, 253, 254], "executed_branches": [[70, 71], [70, 73], [183, 184], [183, 185], [185, 189], [198, 199], [248, 251], [251, 256]], "missing_branches": [[140, 142], [140, 164], [148, 149], [148, 150], [150, 151], [150, 152], [152, 154], [152, 158], [155, 156], [155, 166], [185, 191], [198, 201], [221, 223], [221, 224], [248, 249], [251, 252]], "summary": {"covered_lines": 113, "missing_lines": 70, "covered_branches": 8, "missing_branches": 16, "percent_covered": 58.45410628019324}}, "dataclasses_json/utils.py": {"executed_lines": [1, 2, 3, 4, 7, 9, 25, 29, 32, 33, 34, 35, 43, 44, 47, 48, 49, 50, 51, 53, 56, 57, 58, 62, 65, 66, 67, 68, 69, 70, 71, 74, 86, 87, 90, 91, 92, 93, 96, 97, 100, 101, 104, 109, 115, 116, 117, 120, 121, 129, 134, 135, 136, 137, 156], "missing_lines": [10, 11, 12, 13, 14, 15, 16, 18, 20, 21, 22, 23, 26, 36, 37, 38, 39, 41, 59, 60, 75, 77, 78, 80, 81, 82, 83, 105, 106, 110, 111, 112, 118, 119, 123, 124, 126, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 149, 150, 151], "executed_branches": [[9, 25], [35, 43], [136, 137]], "missing_branches": [[9, 10], [35, 36], [77, 78], [77, 80], [117, 118], [117, 119], [123, 124], [123, 126], [136, 138], [138, 139], [138, 141], [141, 142], [141, 144], [144, 145], [144, 146], [146, 147], [146, 149]], "summary": {"covered_lines": 55, "missing_lines": 50, "covered_branches": 3, "missing_branches": 17, "percent_covered": 46.4}}, "dataclasses_json/core.py": {"executed_lines": [1, 2, 3, 4, 6, 11, 12, 13, 14, 15, 17, 19, 20, 26, 28, 29, 32, 33, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 50, 53, 54, 55, 56, 57, 58, 59, 61, 63, 65, 67, 68, 69, 71, 72, 73, 75, 76, 78, 80, 83, 85, 86, 87, 90, 91, 93, 96, 97, 98, 99, 100, 103, 104, 105, 106, 107, 109, 110, 112, 114, 115, 118, 120, 121, 122, 123, 124, 125, 127, 130, 131, 133, 134, 135, 136, 137, 138, 140, 141, 142, 149, 151, 152, 153, 156, 159, 160, 161, 162, 163, 164, 171, 172, 173, 175, 176, 177, 181, 182, 189, 194, 197, 198, 199, 200, 205, 206, 208, 211, 212, 216, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 230, 231, 234, 235, 236, 237, 238, 241, 242, 243, 244, 247, 249, 250, 251, 254, 255, 256, 258, 262, 263, 264, 265, 267, 269, 270, 271, 272, 274, 277, 280, 283, 290, 291, 292, 295, 305, 306, 307, 308, 309, 311, 312, 315, 320, 321, 322, 323, 324, 326, 327, 328, 329, 330, 334, 335, 336, 338], "missing_lines": [46, 47, 49, 60, 62, 64, 66, 77, 79, 81, 92, 113, 126, 132, 143, 144, 145, 146, 157, 165, 166, 169, 179, 184, 185, 187, 188, 195, 201, 202, 203, 217, 273, 275, 279, 331, 332, 333], "executed_branches": [[35, 36], [35, 40], [36, 37], [36, 39], [40, 41], [40, 42], [42, 43], [42, 44], [44, 45], [58, 59], [58, 65], [59, 61], [61, 63], [63, 58], [72, 73], [72, 87], [76, 78], [78, 80], [80, 83], [91, 93], [98, 99], [98, 115], [99, 100], [99, 112], [103, 104], [103, 105], [112, 114], [121, 122], [121, 127], [123, 121], [123, 124], [125, 121], [131, 133], [140, 141], [140, 149], [141, 142], [153, 156], [153, 208], [156, 159], [161, 162], [161, 175], [164, 171], [175, 176], [176, 177], [181, 189], [189, 194], [189, 200], [194, 197], [200, 205], [212, 216], [212, 221], [216, 219], [221, 222], [221, 225], [225, 226], [225, 230], [242, 243], [242, 244], [244, 247], [244, 249], [249, 250], [249, 267], [250, 251], [250, 258], [267, 269], [267, 270], [270, 271], [272, 274], [274, 277], [305, 306], [305, 308], [308, 309], [308, 311], [320, 321], [320, 330], [322, 323], [322, 326], [330, 334], [334, 336], [334, 338]], "missing_branches": [[44, 46], [46, 47], [46, 49], [59, 60], [61, 62], [63, 64], [76, 77], [78, 79], [80, 81], [91, 92], [112, 113], [125, 126], [131, 132], [141, 143], [143, 144], [143, 145], [145, 140], [145, 146], [156, 157], [164, 165], [176, 179], [181, 184], [184, 185], [184, 187], [194, 195], [200, 201], [216, 217], [270, 279], [272, 273], [274, 275], [330, 331]], "summary": {"covered_lines": 189, "missing_lines": 38, "covered_branches": 80, "missing_branches": 31, "percent_covered": 79.58579881656804}}, "dataclasses_json/mm.py": {"executed_lines": [3, 4, 5, 6, 8, 9, 10, 11, 12, 14, 16, 17, 18, 20, 22, 29, 30, 31, 32, 34, 35, 37, 39, 40, 41, 43, 44, 46, 49, 50, 51, 52, 54, 55, 57, 59, 60, 61, 63, 64, 66, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 82, 83, 84, 96, 97, 98, 104, 105, 106, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 136, 137, 138, 139, 140, 142, 143, 144, 146, 152, 153, 155, 156, 157, 161, 162, 165, 166, 169, 170, 171, 174, 175, 178, 179, 182, 183, 184, 185, 186, 190, 191, 192, 193, 196, 197, 198, 201, 202, 203, 204, 210, 211, 212, 213, 216, 217, 218, 222, 227, 228, 229, 230, 231, 235, 251, 252, 253, 255, 256, 258, 259, 261, 264, 265, 266, 267, 268, 275, 278, 279, 280, 283, 284, 285, 286, 289, 290, 291, 292, 294, 297, 300, 307, 310, 312, 313, 315, 318, 321, 322, 323, 324, 326, 327, 332, 333, 334, 336, 342, 343, 349, 355, 356, 357, 359, 360, 361, 362, 363, 364, 365, 366, 367, 369], "missing_lines": [85, 86, 87, 89, 90, 92, 94, 99, 100, 101, 102, 103, 108, 109, 111, 113, 159, 163, 167, 172, 176, 180, 188, 194, 199, 208, 214, 219, 224, 233, 236, 237, 238, 239, 240, 242, 243, 244, 247, 249, 262, 270, 271, 273, 287, 293, 295, 298, 301, 302, 303, 305, 308, 337, 338, 340, 350, 351, 352, 353], "executed_branches": [[31, 32], [31, 34], [34, 35], [34, 37], [40, 41], [40, 43], [43, 44], [43, 46], [51, 52], [51, 54], [54, 55], [54, 57], [60, 61], [60, 63], [63, 64], [63, 66], [77, 78], [77, 79], [79, 80], [80, 81], [81, 82], [98, 104], [104, 105], [105, 106], [142, 143], [229, 230], [230, 231], [235, 251], [255, 256], [255, 258], [258, 259], [258, 261], [261, 264], [264, 265], [283, 284], [283, 315], [286, 289], [292, 294], [294, 297], [297, 300], [300, 307], [307, 310], [312, 313], [349, 355]], "missing_branches": [[79, 89], [80, 86], [81, 85], [86, 79], [86, 87], [98, 99], [100, 101], [100, 104], [101, 100], [101, 102], [104, 108], [105, 104], [142, 224], [230, 233], [235, 236], [236, 237], [236, 242], [261, 262], [264, 270], [286, 287], [292, 293], [294, 295], [297, 298], [300, 301], [303, 305], [303, 307], [307, 308], [312, 283], [337, 338], [337, 340], [349, 350], [350, 351], [350, 357]], "summary": {"covered_lines": 195, "missing_lines": 60, "covered_branches": 44, "missing_branches": 33, "percent_covered": 71.98795180722891}}, "combined_tests_dataclasses-json_445598cb/test_case_002_642dae.py": {"executed_lines": [1, 2, 4, 6, 9, 10, 11, 14, 17, 20, 21, 24, 25], "missing_lines": [15, 18], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 13, "missing_lines": 2, "covered_branches": 0, "missing_branches": 0, "percent_covered": 86.66666666666667}}, "combined_tests_dataclasses-json_445598cb/test_case_003_134c02.py": {"executed_lines": [1, 2, 4, 5, 8, 9], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_004_715af0.py": {"executed_lines": [1, 2, 4, 6, 9, 13, 16, 17, 18], "missing_lines": [10], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 1, "covered_branches": 0, "missing_branches": 0, "percent_covered": 90.0}}, "combined_tests_dataclasses-json_445598cb/test_case_005_419868.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13, 14, 15, 16, 19, 20, 21, 22, 23], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 15, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_006_c92daa.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 10, 12, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_007_86bcb6.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14, 15, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_008_0df272.py": {"executed_lines": [1, 2, 4, 6, 10, 13, 16, 17], "missing_lines": [7], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 1, "covered_branches": 0, "missing_branches": 0, "percent_covered": 88.88888888888889}}, "combined_tests_dataclasses-json_445598cb/test_case_009_cdb22b.py": {"executed_lines": [1, 4, 5, 7, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_010_db6a56.py": {"executed_lines": [1, 4, 6, 7, 9, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_011_ea2833.py": {"executed_lines": [1, 3, 4, 6, 8, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_012_e180d6.py": {"executed_lines": [1, 2, 4, 5, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_013_c494e9.py": {"executed_lines": [1, 2, 4, 5, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_014_322d00.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 11, 14, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_015_33fe99.py": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 12, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_016_e1d7b0.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 9, 10, 11, 12, 15, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_017_a045db.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 12, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_018_d49291.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 11, 14, 17, 20], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_019_ec6569.py": {"executed_lines": [1, 2, 3, 5, 6, 8, 10, 11, 14, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_020_a13b9f.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_021_be8d26.py": {"executed_lines": [1, 2, 4, 5, 6, 7, 8, 9, 10, 14, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_022_344630.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 10, 14, 15, 16, 17, 21, 24], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 14, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_023_20151c.py": {"executed_lines": [1, 3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 31, 33], "missing_lines": [], "executed_branches": [[9, 10], [9, 15], [11, 12], [13, 14]], "missing_branches": [[11, 9], [13, 9]], "summary": {"covered_lines": 25, "missing_lines": 0, "covered_branches": 4, "missing_branches": 2, "percent_covered": 93.54838709677419}}, "combined_tests_dataclasses-json_445598cb/test_case_024_30f123.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 9, 13, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_025_3b359e.py": {"executed_lines": [1, 2, 4, 5, 6, 8, 10], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_026_e530e4.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 11], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_027_960702.py": {"executed_lines": [1, 2, 4, 5, 6, 7, 8, 9, 12, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_028_30282b.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 9, 10, 14, 16, 18, 20, 21, 22, 23, 27, 30, 31, 32, 33], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 20, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_029_ed2218.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 10, 12, 13, 16, 19, 20], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 13, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_030_73e0b5.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 8, 9, 11, 12, 13, 16, 18, 19, 21, 22], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 16, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_031_ad4cb7.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 9, 11, 13, 15, 16, 18, 19, 20, 21, 23, 26, 29, 32, 35], "missing_lines": [14], "executed_branches": [[13, 15]], "missing_branches": [[13, 14]], "summary": {"covered_lines": 20, "missing_lines": 1, "covered_branches": 1, "missing_branches": 1, "percent_covered": 91.30434782608695}}, "combined_tests_dataclasses-json_445598cb/test_case_032_15c384.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 10, 11, 12, 14, 16, 17, 18, 19, 24, 25, 28, 31], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 19, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_033_88d6ae.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 12, 15, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_034_3a8295.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 12, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_035_8ef60d.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 11, 14, 15, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_036_0d23ba.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 11, 14, 17], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_037_95053d.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 10, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_038_41ab33.py": {"executed_lines": [1, 2, 4, 6, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_039_a73069.py": {"executed_lines": [1, 2, 3, 5, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_040_cc9629.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 10, 11], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_041_44f37a.py": {"executed_lines": [1, 2, 4, 6, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_042_9d233e.py": {"executed_lines": [1, 2, 3, 5, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_043_a76a42.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 9, 11, 13, 14, 17, 20], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_044_23fbdf.py": {"executed_lines": [1, 2, 4, 6, 10, 13, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_045_a18e87.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_046_be649a.py": {"executed_lines": [1, 2, 4, 6, 7, 9, 10], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_047_bd4a0d.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 12, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_048_e91e43.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_049_a8a558.py": {"executed_lines": [1, 2, 4, 5, 6, 7, 10, 13, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_050_58a80c.py": {"executed_lines": [1, 2, 4, 5, 7, 8, 11, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_051_f9b90f.py": {"executed_lines": [1, 2, 3, 4, 7, 9, 10, 11, 12, 14, 16, 18, 19, 21, 22, 24, 26, 27, 28], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 19, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_052_305ad8.py": {"executed_lines": [1, 2, 5, 8, 9, 11, 13, 15, 16, 17, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_053_88b2c2.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_054_78eacd.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_055_b60359.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 9, 11, 13, 16, 19, 20, 21, 22, 24], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 16, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_056_1bdd84.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 10, 11, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_057_0e1376.py": {"executed_lines": [1, 2, 4, 5, 6, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_058_d74966.py": {"executed_lines": [1, 2, 4, 5, 6, 7], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_059_4fc1a2.py": {"executed_lines": [1, 2, 4, 6, 9, 12, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_060_2f1b2b.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 10, 11, 12, 14, 15, 16, 17, 19, 21, 24], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 16, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_061_c77ee0.py": {"executed_lines": [1, 2, 4, 5, 6, 8, 9, 12, 14, 16, 17, 18], "missing_lines": [10], "executed_branches": [[8, 9], [16, 0], [16, 17]], "missing_branches": [[8, 10]], "summary": {"covered_lines": 12, "missing_lines": 1, "covered_branches": 3, "missing_branches": 1, "percent_covered": 88.23529411764706}}, "combined_tests_dataclasses-json_445598cb/test_case_062_8de62d.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_063_47dd3b.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_064_b4948e.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_065_8bb36a.py": {"executed_lines": [1, 2, 4, 6, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_066_d7bea9.py": {"executed_lines": [1, 2, 4, 5, 6], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 5, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_067_c2ea08.py": {"executed_lines": [1, 2, 4, 6, 9, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_068_bdfba6.py": {"executed_lines": [1, 2, 4, 5, 6, 8, 10, 11, 14, 17, 20, 21, 26], "missing_lines": [22, 23], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 13, "missing_lines": 2, "covered_branches": 0, "missing_branches": 0, "percent_covered": 86.66666666666667}}, "combined_tests_dataclasses-json_445598cb/test_case_069_21c2a9.py": {"executed_lines": [1, 2, 4, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 25, 26, 27, 28], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 22, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_070_8a28a0.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 10, 12, 13, 16, 19, 20], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 13, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_071_ef8a1d.py": {"executed_lines": [1, 2, 4, 6, 7, 9, 10], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_072_6bd2c9.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 10, 12, 13, 16, 19], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_073_9694c7.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 10, 12, 13, 16, 19], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_074_7889ee.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 8, 9, 11, 12, 13, 14, 15, 18, 19, 21, 22], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 17, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_075_8f6d2e.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 10, 13, 16, 19], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_076_81f65e.py": {"executed_lines": [1, 2, 4, 6, 9, 10, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_077_739df8.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 10, 12, 15, 18, 19], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_078_b5c0d0.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 12, 14, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_079_98749b.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 10, 12, 15, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_080_5320a4.py": {"executed_lines": [1, 2, 3, 6, 8, 9, 11, 14, 17], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_081_ddb974.py": {"executed_lines": [1, 2, 4, 6, 7, 9, 11, 13, 15, 16], "missing_lines": [], "executed_branches": [[13, 0], [13, 15]], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 2, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_082_8d4e76.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 11, 12, 13, 14, 18, 19, 20, 23, 26, 29, 30, 31, 32, 33, 37], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 22, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_083_aaf719.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 12, 13, 16, 19, 22, 25], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 13, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_084_90b5cf.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 8, 10, 12, 15, 16, 18, 20, 21], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 14, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_085_3551f2.py": {"executed_lines": [1, 2, 4, 5, 6, 7], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 6, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_086_35101d.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 9, 10, 11, 12, 16, 18, 20, 21, 22, 25, 28, 31, 32, 33, 34, 35, 40], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 23, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_087_706ff9.py": {"executed_lines": [1, 2, 3, 4, 5, 7, 9, 10, 12, 13, 16, 19, 21, 23, 24, 27, 30, 32, 34, 35, 38, 39], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 22, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_088_b3ed91.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 10, 11, 14, 15], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_089_42c820.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_090_0693fe.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 10, 13, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_091_e7bc6e.py": {"executed_lines": [1, 2, 3, 4, 5, 7, 9, 10, 11, 12, 15, 16, 19, 22, 23, 25, 27, 28, 29, 32, 33], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 21, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_092_851d34.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 8, 9, 11, 13, 14, 15, 18, 21, 24], "missing_lines": [25], "executed_branches": [[24, 0]], "missing_branches": [[24, 25]], "summary": {"covered_lines": 15, "missing_lines": 1, "covered_branches": 1, "missing_branches": 1, "percent_covered": 88.88888888888889}}, "combined_tests_dataclasses-json_445598cb/test_case_093_0ada26.py": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 10, 11, 12, 15, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_094_5c565b.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 12, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_095_b9023f.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 10, 11, 14, 17], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_096_3ccdd2.py": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 12, 15, 16, 17], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_097_3c10bf.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 9, 10, 11, 14, 17], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_098_679719.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 11, 12, 15, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_099_5860e7.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 9, 10], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_100_8f50c9.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 11, 14], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_101_a4a9eb.py": {"executed_lines": [1, 2, 4, 5, 7, 9, 10, 11, 14, 17, 18, 19], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_102_a28800.py": {"executed_lines": [1, 2, 4, 6, 7, 8, 11, 14, 15, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_103_d3a96a.py": {"executed_lines": [1, 2, 3, 4, 6, 7, 8, 9, 11, 13, 16, 17, 20, 23, 26, 29, 32, 33], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 18, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_104_c73fca.py": {"executed_lines": [1, 2, 4, 6, 7, 10, 13], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 7, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_105_dddaa4.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 11, 14, 17, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_106_140201.py": {"executed_lines": [1, 2, 4, 6, 7], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 5, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_107_ccd943.py": {"executed_lines": [1, 2, 4, 6, 7], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 5, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_108_ab3fcb.py": {"executed_lines": [1, 2, 4, 6, 7], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 5, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_109_c711c6.py": {"executed_lines": [1, 2, 3, 5, 6, 8, 9, 11, 13, 14, 15, 18, 21, 24, 27, 28], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 16, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_110_c933db.py": {"executed_lines": [1, 2, 3, 5, 7, 10, 13, 14, 17, 18], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_111_37a7b5.py": {"executed_lines": [1, 2, 5, 6, 8, 9, 11, 12, 14, 15, 16, 17, 18, 19, 22, 23, 24], "missing_lines": [], "executed_branches": [[8, 9], [22, 0], [22, 23]], "missing_branches": [[8, 0]], "summary": {"covered_lines": 17, "missing_lines": 0, "covered_branches": 3, "missing_branches": 1, "percent_covered": 95.23809523809524}}, "combined_tests_dataclasses-json_445598cb/test_case_112_75c394.py": {"executed_lines": [1, 2, 3, 6, 7, 8, 9, 11, 12, 14, 17, 18, 21, 23, 24, 27, 30, 31, 32, 33], "missing_lines": [19], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 20, "missing_lines": 1, "covered_branches": 0, "missing_branches": 0, "percent_covered": 95.23809523809524}}, "combined_tests_dataclasses-json_445598cb/test_case_113_f6b82e.py": {"executed_lines": [1, 2, 3, 5, 7, 10, 13, 16, 19, 22], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 10, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_114_aee849.py": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 10, 11, 12, 15, 18, 19, 20], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 14, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_115_9d122c.py": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 12, 14, 15, 18, 21, 22], "missing_lines": [], "executed_branches": [[8, 9]], "missing_branches": [[8, 10]], "summary": {"covered_lines": 13, "missing_lines": 0, "covered_branches": 1, "missing_branches": 1, "percent_covered": 93.33333333333333}}, "combined_tests_dataclasses-json_445598cb/test_case_116_be5b3d.py": {"executed_lines": [1, 2, 3, 8, 9, 10, 13, 14, 15, 17, 18, 21, 23, 24, 27, 30, 31, 33], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 18, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_117_14e0b8.py": {"executed_lines": [1, 2, 3, 5, 6, 7, 8, 9, 11, 13, 16, 19, 22, 23, 26, 27, 30, 31, 32], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 19, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_118_bc1d81.py": {"executed_lines": [1, 4, 5, 6, 8, 10, 12, 14, 15, 18, 21, 22], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 12, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_119_cd3699.py": {"executed_lines": [1, 3, 4, 5, 6, 9, 10, 12, 13, 15, 16], "missing_lines": [], "executed_branches": [[5, 6]], "missing_branches": [[5, 0]], "summary": {"covered_lines": 11, "missing_lines": 0, "covered_branches": 1, "missing_branches": 1, "percent_covered": 92.3076923076923}}, "combined_tests_dataclasses-json_445598cb/test_case_120_52e42f.py": {"executed_lines": [1, 2, 5, 6, 9, 10, 15, 16, 19, 22, 23, 26, 27], "missing_lines": [7, 11, 12, 13], "executed_branches": [], "missing_branches": [[11, 12], [11, 13]], "summary": {"covered_lines": 13, "missing_lines": 4, "covered_branches": 0, "missing_branches": 2, "percent_covered": 68.42105263157895}}, "combined_tests_dataclasses-json_445598cb/test_case_121_2eefd8.py": {"executed_lines": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 13, 15, 17, 19, 20, 21, 25, 28, 31], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 19, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_122_cf262f.py": {"executed_lines": [1, 2, 3, 5, 7, 10, 13, 16], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 8, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_123_bc85cf.py": {"executed_lines": [1, 3, 4, 6, 7, 10, 12, 13, 16, 17, 19], "missing_lines": [8], "executed_branches": [[6, 7]], "missing_branches": [[6, 8]], "summary": {"covered_lines": 11, "missing_lines": 1, "covered_branches": 1, "missing_branches": 1, "percent_covered": 85.71428571428571}}, "combined_tests_dataclasses-json_445598cb/test_case_124_a28619.py": {"executed_lines": [1, 2, 4, 5, 7, 8, 10, 12, 13, 14, 15, 17, 18, 19, 23, 26], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 16, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/test_case_125_481635.py": {"executed_lines": [1, 2, 5, 6, 7, 9, 10, 11, 12], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 9, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}, "combined_tests_dataclasses-json_445598cb/__init__.py": {"executed_lines": [1], "missing_lines": [], "executed_branches": [], "missing_branches": [], "summary": {"covered_lines": 1, "missing_lines": 0, "covered_branches": 0, "missing_branches": 0, "percent_covered": 100.0}}}, "summary": {"covered_lines": 2057, "missing_lines": 274, "covered_branches": 172, "missing_branches": 118, "percent_covered": 85.0438763830599}}