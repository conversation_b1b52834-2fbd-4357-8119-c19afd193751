import pytest
from dataclasses_json.cfg import config

def test_config_initialization_with_none_metadata():
    # Test behavior when metadata is None
    result = config(metadata=None, encoder=lambda x: x, decoder=lambda x: x)
    
    # Assert that the result is a dictionary with 'dataclasses_json' key initialized
    assert isinstance(result, dict)
    assert 'dataclasses_json' in result
    
    # Assert that the 'dataclasses_json' contains correct encoder and decoder keys
    assert 'encoder' in result['dataclasses_json']
    assert 'decoder' in result['dataclasses_json']
    
    # Assert that the encoder and decoder are callable (not comparing instance here)
    assert callable(result['dataclasses_json']['encoder'])
    assert callable(result['dataclasses_json']['decoder'])