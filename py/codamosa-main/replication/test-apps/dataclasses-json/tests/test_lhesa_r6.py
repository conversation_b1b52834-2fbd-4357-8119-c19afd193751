import pytest
from dataclasses import dataclass
from typing import Dict, Callable
from marshmallow import fields as MarshmallowField
from dataclasses_json.cfg import _GlobalConfig

def test_global_config_initialization():
    """Test that the dictionaries are initialized as empty upon instantiation of _GlobalConfig."""
    config = _GlobalConfig()
    assert config.encoders == {}
    assert config.decoders == {}
    assert config.mm_fields == {}