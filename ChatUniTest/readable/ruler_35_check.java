package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Test;
import java.io.StringReader;
import java.io.Reader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class RuleCompilerTest {

    @Test
    public void testCheckValidRule() {
        String validRuleJson = "{\"field\": [\"value1\", \"value2\"]}";
        Reader reader = new StringReader(validRuleJson);
        String result = RuleCompiler.check(reader);
        assertNull("Valid rule should return null", result);
    }

    @Test
    public void testCheckInvalidRuleSyntax() {
        String invalidRuleJson = "{\"field\": [\"value1\", \"value2\""; // Missing closing bracket
        Reader reader = new StringReader(invalidRuleJson);
        String result = RuleCompiler.check(reader);
        assertNotNull("Invalid rule syntax should return error message", result);
        assertTrue("Error message should contain 'Unexpected end-of-input'", result.contains("Unexpected end-of-input"));
    }

    @Test
    public void testCheckInvalidRuleStructure() {
        String invalidRuleJson = "{\"field\": \"value1\"}"; // value should be an array
        Reader reader = new StringReader(invalidRuleJson);
        String result = RuleCompiler.check(reader);
        assertNotNull("Invalid rule structure should return error message", result);
        assertTrue("Error message should contain 'must be an object or an array'", result.contains("must be an object or an array"));
    }

    @Test
    public void testCheckEmptyObject() {
        String invalidRuleJson = "{}"; // Empty object
        Reader reader = new StringReader(invalidRuleJson);
        String result = RuleCompiler.check(reader);
        assertNotNull("Empty object should return error message", result);
        assertTrue("Error message should contain 'Empty objects are not allowed'", result.contains("Empty objects are not allowed"));
    }

    @Test
    public void testCheckViaInputStream() {
        String validRuleJson = "{\"field\": [\"value1\", \"value2\"]}";
        InputStream inputStream = new ByteArrayInputStream(validRuleJson.getBytes(StandardCharsets.UTF_8));
        String result = RuleCompiler.check(inputStream);
        assertNull("Valid rule with InputStream should return null", result);
    }

    @Test
    public void testCheckInvalidJsonToken() {
        String invalidRuleJson = "notAJson{}"; // Not a JSON object
        Reader reader = new StringReader(invalidRuleJson);
        String result = RuleCompiler.check(reader);
        assertNotNull("Invalid JSON token should return error message", result);
    }
}