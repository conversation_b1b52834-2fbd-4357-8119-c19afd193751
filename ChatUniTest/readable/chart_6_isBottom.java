package org.jfree.chart.ui;

import static org.junit.Assert.*;
import org.junit.Test;

public class TextAnchorTest {

    @Test
    public void testIsBottom_Left() {
        // Arrange
        TextAnchor anchor = TextAnchor.BOTTOM_LEFT;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertTrue("BOTTOM_LEFT anchor should return true for isBottom()", result);
    }

    @Test
    public void testIsBottom_Center() {
        // Arrange
        TextAnchor anchor = TextAnchor.BOTTOM_CENTER;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertTrue("BOTTOM_CENTER anchor should return true for isBottom()", result);
    }

    @Test
    public void testIsBottom_Right() {
        // Arrange
        TextAnchor anchor = TextAnchor.BOTTOM_RIGHT;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertTrue("BOTTOM_RIGHT anchor should return true for isBottom()", result);
    }

    @Test
    public void testIsBottom_NotBottom() {
        // Arrange
        TextAnchor anchor = TextAnchor.TOP_LEFT;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertFalse("TOP_LEFT anchor should return false for isBottom()", result);
    }

    @Test
    public void testIsBottom_OtherCenters_NotBottom() {
        // Arrange
        TextAnchor anchor = TextAnchor.BASELINE_CENTER;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertFalse("BASELINE_CENTER anchor should return false for isBottom()", result);
    }
    
    @Test
    public void testIsBottom_MiddleLeft_NotBottom() {
        // Arrange
        TextAnchor anchor = TextAnchor.CENTER_LEFT;

        // Act
        boolean result = anchor.isBottom();

        // Assert
        assertFalse("CENTER_LEFT anchor should return false for isBottom()", result);
    }
}