package org.jfree.chart.ui;

// package org.jfree.chart.ui;

import static org.junit.Assert.*;

import java.awt.event.WindowEvent;
import javax.swing.JFrame;
import org.junit.Before;
import org.junit.Test;

public class ApplicationFrameTest {

    private ApplicationFrame applicationFrame;

    @Before
    public void setUp() {
        this.applicationFrame = new ApplicationFrame("Test Frame");
        this.applicationFrame.setSize(300, 200); // Ensure the frame has a size
        this.applicationFrame.setVisible(true);  // Make the frame visible for the test
    }

    @Test
    public void testWindowDeactivated() {
        // Create a WindowEvent with the source set to the application frame.
        WindowEvent event = new WindowEvent(applicationFrame, WindowEvent.WINDOW_DEACTIVATED);

        // Test windowDeactivated method. Since it does nothing, we simply check that no exception is thrown.
        try {
            applicationFrame.windowDeactivated(event);
        } catch (Exception e) {
            fail("The method windowDeactivated threw an exception: " + e.getMessage());
        }

        // The following assertions ensure that the state of the application frame is unchanged.
        assertTrue(applicationFrame.isDisplayable()); // The frame should still be displayable since we're not disposing it.
        assertTrue(applicationFrame.isVisible()); // Visibility shouldn't change.
        assertEquals("Test Frame", applicationFrame.getTitle()); // Title should remain unchanged.
    }
}