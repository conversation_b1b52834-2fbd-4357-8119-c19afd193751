package com.google.gson.internal;

import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class NonNullElementWrapperListTest {

    private NonNullElementWrapperList<String> nonNullList;

    @Before
    public void setUp() {
        ArrayList<String> delegateList = new ArrayList<>();
        delegateList.add("one");
        delegateList.add("two");
        delegateList.add("three");
        nonNullList = new NonNullElementWrapperList<>(delegateList);
    }

    @Test
    public void testContains_ExistingElement_ReturnsTrue() {
        assertTrue(nonNullList.contains("one"));
        assertTrue(nonNullList.contains("two"));
        assertTrue(nonNullList.contains("three"));
    }

    @Test
    public void testContains_NonExistingElement_ReturnsFalse() {
        assertFalse(nonNullList.contains("four"));
        assertFalse(nonNullList.contains("five"));
    }

    @Test
    public void testContains_NullElement_ReturnsFalse() {
        // Test that contains method does not throw an exception and returns false for null elements
        assertFalse(nonNullList.contains(null));
    }
}