package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

// Note: Import statements for AnythingButValuesSet and MatchType are assumed to be correct

public class PatternsTest {

    @Test
    public void testAnythingButIgnoreCaseMatchWithSingleValue() {
        Set<String> singleSet = Collections.singleton("testValue");
        AnythingButValuesSet result = Patterns.anythingButIgnoreCaseMatch(singleSet);

        assertNotNull("Result should not be null", result);
        assertEquals("Mismatch in MatchType", MatchType.ANYTHING_BUT_IGNORE_CASE, result.type());
        assertEquals("Mismatch in values set size", 1, result.getValues().size());
        assertTrue("Values set should contain 'testValue'", result.getValues().contains("testValue"));
    }

    @Test
    public void testAnythingButIgnoreCaseMatchWithMultipleValues() {
        Set<String> multipleSet = new HashSet<>();
        multipleSet.add("value1");
        multipleSet.add("value2");
        AnythingButValuesSet result = Patterns.anythingButIgnoreCaseMatch(multipleSet);

        assertNotNull("Result should not be null", result);
        assertEquals("Mismatch in MatchType", MatchType.ANYTHING_BUT_IGNORE_CASE, result.type());
        assertEquals("Mismatch in values set size", 2, result.getValues().size());
        assertTrue("Values set should contain 'value1'", result.getValues().contains("value1"));
        assertTrue("Values set should contain 'value2'", result.getValues().contains("value2"));
    }

    @Test
    public void testAnythingButIgnoreCaseMatchWithEmptySet() {
        Set<String> emptySet = Collections.emptySet();
        AnythingButValuesSet result = Patterns.anythingButIgnoreCaseMatch(emptySet);

        assertNotNull("Result should not be null", result);
        assertEquals("Mismatch in MatchType", MatchType.ANYTHING_BUT_IGNORE_CASE, result.type());
        assertTrue("Values set should be empty", result.getValues().isEmpty());
    }

    @Test
    public void testAnythingButIgnoreCaseMatchWithNullSet() {
        try {
            Patterns.anythingButIgnoreCaseMatch((Set<String>) null);
            fail("Expected NullPointerException to be thrown");
        } catch (NullPointerException e) {
            // This is expected, so do nothing
        }
    }
}