package org.jfree.chart.ui;

import static org.junit.Assert.*;
import org.junit.Test;

public class HorizontalAlignmentTest {

    @Test
    public void testEquals_SameInstance() {
        // Test equality when same instance is compared
        assertTrue(HorizontalAlignment.LEFT.equals(HorizontalAlignment.LEFT));
        assertTrue(HorizontalAlignment.RIGHT.equals(HorizontalAlignment.RIGHT));
        assertTrue(HorizontalAlignment.CENTER.equals(HorizontalAlignment.CENTER));
    }

    @Test
    public void testEquals_DifferentInstancesSameValue() {
        // Test equality when different instances with the same value are compared
        HorizontalAlignment leftCopy = HorizontalAlignment.LEFT;
        HorizontalAlignment rightCopy = HorizontalAlignment.RIGHT;
        HorizontalAlignment centerCopy = HorizontalAlignment.CENTER;

        assertTrue(HorizontalAlignment.LEFT.equals(leftCopy));
        assertTrue(HorizontalAlignment.RIGHT.equals(rightCopy));
        assertTrue(HorizontalAlignment.CENTER.equals(centerCopy));
    }

    @Test
    public void testEquals_DifferentTypes() {
        // Test inequality when compared with an object of a different type
        assertFalse(HorizontalAlignment.LEFT.equals("HorizontalAlignment.LEFT"));
        assertFalse(HorizontalAlignment.RIGHT.equals(42));
        assertFalse(HorizontalAlignment.CENTER.equals(null));
    }

    @Test
    public void testEquals_Null() {
        // Test inequality when compared with null
        assertFalse(HorizontalAlignment.LEFT.equals(null));
        assertFalse(HorizontalAlignment.RIGHT.equals(null));
        assertFalse(HorizontalAlignment.CENTER.equals(null));
    }

    @Test
    public void testEquals_DifferentAlignment() {
        // Test inequality between different HorizontalAlignment constants
        assertFalse(HorizontalAlignment.LEFT.equals(HorizontalAlignment.RIGHT));
        assertFalse(HorizontalAlignment.LEFT.equals(HorizontalAlignment.CENTER));
        assertFalse(HorizontalAlignment.RIGHT.equals(HorizontalAlignment.LEFT));
        assertFalse(HorizontalAlignment.RIGHT.equals(HorizontalAlignment.CENTER));
        assertFalse(HorizontalAlignment.CENTER.equals(HorizontalAlignment.LEFT));
        assertFalse(HorizontalAlignment.CENTER.equals(HorizontalAlignment.RIGHT));
    }
}