package com.google.gson;

// package com.google.gson;

import org.junit.Before;
import org.junit.Test;
import org.junit.Assert;
import java.util.List;

public class JsonArrayTest {

    private JsonArray emptyJsonArray;
    private JsonArray populatedJsonArray;

    @Before
    public void setUp() {
        emptyJsonArray = new JsonArray();
        populatedJsonArray = new JsonArray();
        populatedJsonArray.add(new JsonPrimitive(1));
        populatedJsonArray.add(new JsonPrimitive("test"));
        populatedJsonArray.add(new JsonPrimitive(true));
    }

    @Test
    public void testAsListOnEmptyArray() {
        List<JsonElement> list = emptyJsonArray.asList();
        Assert.assertNotNull("The list should not be null", list);
        Assert.assertTrue("The list should be empty", list.isEmpty());
    }

    @Test
    public void testAsListReturnsCorrectSize() {
        List<JsonElement> list = populatedJsonArray.asList();
        Assert.assertEquals("The size of the list should match the JsonArray size", 3, list.size());
    }

    @Test
    public void testAsListReturnsCorrectElements() {
        List<JsonElement> list = populatedJsonArray.asList();
        Assert.assertEquals("The first element should be a JsonPrimitive with value 1",
                new JsonPrimitive(1), list.get(0));
        Assert.assertEquals("The second element should be a JsonPrimitive with value 'test'",
                new JsonPrimitive("test"), list.get(1));
        Assert.assertEquals("The third element should be a JsonPrimitive with value true",
                new JsonPrimitive(true), list.get(2));
    }

    @Test
    public void testAsListAddNullThrowsException() {
        List<JsonElement> list = populatedJsonArray.asList();
        try {
            list.add(null);
            Assert.fail("Adding null should throw NullPointerException");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }

    @Test
    public void testAsListReflectsChangesInJsonArray() {
        List<JsonElement> list = emptyJsonArray.asList();
        JsonElement newElement = new JsonPrimitive("new element");
        emptyJsonArray.add(newElement);

        Assert.assertTrue("The list should contain the new element added to the JsonArray", 
                          list.contains(newElement));
    }

    @Test
    public void testJsonArrayReflectsChangesInList() {
        List<JsonElement> list = emptyJsonArray.asList();
        JsonElement newElement = new JsonPrimitive("new element");
        list.add(newElement);

        Assert.assertTrue("The JsonArray should contain the new element added to the list", 
                          emptyJsonArray.contains(newElement));
    }
}