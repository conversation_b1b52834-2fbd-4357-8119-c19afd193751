package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Unit tests for the Option class.
 */
public class OptionTest {

    /**
     * Test the builder method to ensure it returns a non-null Builder instance.
     */
    @Test
    public void testBuilderReturnsNonNullInstance() {
        Option.Builder builder = Option.builder();
        assertNotNull("Builder instance should not be null", builder);
    }
    
    /**
     * Test the builder with valid option to ensure it initializes properly.
     */
    @Test
    public void testBuilderWithValidOption() {
        Option.Builder builder = Option.builder("s");
        Option option = builder.build();
        assertNotNull("Option should not be null", option);
        assertEquals("Option short name should match", "s", option.getOpt());
    }

    /**
     * Test the builder with null option and a long name to ensure it constructs a valid Option.
     */
    @Test
    public void testBuilderWithNullOption() {
        Option.Builder builder = Option.builder(null).longOpt("longName");
        Option option = builder.build();
        assertNotNull("Option should not be null", option);
        assertNull("Option short name should be null", option.getOpt());
        assertEquals("Long option name should match", "longName", option.getLongOpt());
    }

    /**
     * Test the builder with no option provided to ensure it throws IllegalArgumentException.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBuilderWithNoOptionThrowsException() {
        Option.builder().build();
    }

    /**
     * Test builder with invalid option to ensure it throws IllegalArgumentException.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBuilderWithInvalidOption() {
        Option.Builder builder = Option.builder("invalid^char");
    }

}