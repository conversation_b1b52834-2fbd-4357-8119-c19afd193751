package org.jfree.chart.ui;

import static org.junit.Assert.assertEquals;

import java.awt.Dimension;
import java.awt.Paint;
import javax.swing.JComponent;
import org.junit.Test;

public class PaintSampleTest {

    @Test
    public void testGetPreferredSize_DefaultSize() {
        // create a PaintSample with a dummy paint
        PaintSample paintSample = new PaintSample(null);
        
        // expected preferred size
        Dimension expectedSize = new Dimension(80, 12);
        
        // assert that the getPreferredSize returns the expected size
        assertEquals("The preferred size should be 80x12 for a default PaintSample", 
                     expectedSize, paintSample.getPreferredSize());
    }
}