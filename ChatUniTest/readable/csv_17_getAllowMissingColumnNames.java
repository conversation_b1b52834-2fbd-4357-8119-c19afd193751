package org.apache.commons.csv;

import static org.junit.Assert.*;
import org.junit.Test;

public class CSVFormatTest {

    @Test
    public void testGetAllowMissingColumnNamesTrue() {
        // Create a CSVFormat instance that allows missing column names
        CSVFormat csvFormat = CSVFormat.DEFAULT.withAllowMissingColumnNames(true);
        
        // Check if the allowMissingColumnNames flag is set to true
        assertTrue(csvFormat.getAllowMissingColumnNames());
    }

    @Test
    public void testGetAllowMissingColumnNamesFalse() {
        // Create a CSVFormat instance that does not allow missing column names
        CSVFormat csvFormat = CSVFormat.DEFAULT.withAllowMissingColumnNames(false);
        
        // Check if the allowMissingColumnNames flag is set to false
        assertFalse(csvFormat.getAllowMissingColumnNames());
    }

    @Test
    public void testBuilderGetAllowMissingColumnNamesTrue() {
        // Use the builder to set allowMissingColumnNames to true
        CSVFormat csvFormat = CSVFormat.Builder.create()
                                .setAllowMissingColumnNames(true)
                                .build();

        // Assert the allowMissingColumnNames flag is true
        assertTrue(csvFormat.getAllowMissingColumnNames());
    }

    @Test
    public void testBuilderGetAllowMissingColumnNamesFalse() {
        // Use the builder to set allowMissingColumnNames to false
        CSVFormat csvFormat = CSVFormat.Builder.create()
                                .setAllowMissingColumnNames(false)
                                .build();

        // Assert the allowMissingColumnNames flag is false
        assertFalse(csvFormat.getAllowMissingColumnNames());
    }
}