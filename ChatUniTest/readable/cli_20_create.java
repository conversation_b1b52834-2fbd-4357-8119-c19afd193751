package org.apache.commons.cli;

// package org.apache.commons.cli;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class OptionBuilderTest {

    @Before
    public void setUp() {
        // No need to reset manually, since option attributes are static and each test uses the builder independently.
    }

    @After
    public void tearDown() {
        // Clean up actions are not necessary as OptionBuilder does not hold instance-specific state across tests.
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateWithoutLongOpt() {
        // Attempt to create an Option without a long option
        OptionBuilder.create();
    }

    @Test
    public void testCreateWithLongOpt() {
        // Set up long option
        String longOpt = "testOption";
        OptionBuilder.withLongOpt(longOpt);

        // Attempt to create an Option with a long option
        Option result = OptionBuilder.create();

        // Assertions to ensure correct Option creation
        assertNotNull("Option should not be null", result);
        assertEquals("Long option should match", longOpt, result.getLongOpt());
        assertFalse("Option should not be required by default", result.isRequired());
        assertNull("Description should be null by default", result.getDescription());
        assertEquals("Argument count should be UNINITIALIZED by default", Option.UNINITIALIZED, result.getArgs());
        assertFalse("Option should not have optional arg by default", result.hasOptionalArg());
        assertNull("Argument name should be null by default", result.getArgName());
        assertEquals("Type should be String by default", String.class, result.getType());
        assertEquals("Value separator should be the default char", 0, result.getValueSeparator());
    }

    @Test
    public void testCreateWithFullyConfiguredOption() {
        // Configure the Option with various properties
        String longOpt = "complexOption";
        String description = "This is a complex test option";
        String argName = "arg";
        char valueSeparator = ':';
        
        OptionBuilder.withLongOpt(longOpt)
                     .withDescription(description)
                     .withArgName(argName)
                     .hasArg()
                     .isRequired(true)
                     .withValueSeparator(valueSeparator);

        // Create Option
        Option result = OptionBuilder.create();

        // Assertions for all properties
        assertNotNull("Option should not be null", result);
        assertEquals("Long option should match", longOpt, result.getLongOpt());
        assertEquals("Description should match", description, result.getDescription());
        assertTrue("Option should be required", result.isRequired());
        assertEquals("Argument count should be 1", 1, result.getArgs());
        assertEquals("Argument name should match", argName, result.getArgName());
        assertEquals("Value separator should match", valueSeparator, result.getValueSeparator());
        assertFalse("Option should not have optional arg", result.hasOptionalArg());
        assertEquals("Type should be String by default", String.class, result.getType());
    }
}