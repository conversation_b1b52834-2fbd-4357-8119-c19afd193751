package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;

public class CSVFormatTest {

    @Test
    public void testGetAutoFlushDefaultFormat() {
        CSVFormat format = CSVFormat.DEFAULT;
        // For the default format, verify if autoFlush is set to false
        assertFalse("Default CSVFormat should have autoFlush set to false", format.getAutoFlush());
    }

    @Test
    public void testGetAutoFlushExcelFormat() {
        CSVFormat format = CSVFormat.EXCEL;
        // Verify if excel format retains the autoFlush property of the base format (DEFAULT)
        assertFalse("EXCEL CSVFormat should inherit autoFlush == false from DEFAULT", format.getAutoFlush());
    }

    @Test
    public void testGetAutoFlushCustomFormat() {
        CSVFormat format = CSVFormat.DEFAULT.builder().setAutoFlush(true).build();
        // Verify the customized format has autoFlush set to true
        assertTrue("Customized CSVFormat should have autoFlush set to true", format.getAutoFlush());
    }

    @Test
    public void testGetAutoFlushCustomBuilder() {
        CSVFormat.Builder builder = CSVFormat.Builder.create(CSVFormat.DEFAULT);
        builder.setAutoFlush(true);
        CSVFormat format = builder.build();
        // Verify the format built with the customized builder has autoFlush set to true
        assertTrue("Builder-customized CSVFormat should have autoFlush set to true", format.getAutoFlush());
    }

    @Test
    public void testGetAutoFlushVariousFormats() {
        CSVFormat[] formats = {
            CSVFormat.INFORMIX_UNLOAD,
            CSVFormat.INFORMIX_UNLOAD_CSV,
            CSVFormat.MYSQL,
            CSVFormat.RFC4180,
            CSVFormat.POSTGRESQL_CSV,
            CSVFormat.POSTGRESQL_TEXT,
            CSVFormat.TDF
        };

        for (CSVFormat format : formats) {
            // Verify if various predefined formats retain the default autoFlush behavior
            assertFalse("Predefined CSVFormat " + format + " should have autoFlush == false", format.getAutoFlush());
        }
    }
}