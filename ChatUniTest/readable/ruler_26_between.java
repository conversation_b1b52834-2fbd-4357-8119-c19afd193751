package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;

public class RangeTest {

    @Test
    public void testBetweenValidRange() {
        // Test with standard numerical range
        Range range = Range.between("1", false, "100", true);
        assertNotNull("Range object should not be null", range);
        assertFalse("Open bottom should be false", range.openBottom);
        assertTrue("Open top should be true", range.openTop);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBetweenInvalidRangeEqual() {
        // Test where bottom and top are the same, which should throw an exception
        Range.between("50", false, "50", false);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBetweenInvalidRangeBottomGreaterThanTop() {
        // Test where bottom is greater than top, which should throw an exception
        Range.between("200", false, "100", true);
    }

    @Test
    public void testBetweenHexRange() {
        // Adjusted test that treats "9" and "10" (A in hex) as decimal numbers
        Range range = Range.between("9", true, "10", false);
        assertNotNull("Range object should not be null", range);
        assertTrue("Open bottom should be true", range.openBottom);
        assertFalse("Open top should be false", range.openTop);
    }

    @Test
    public void testBetween_MinMax_Digits() {
        // Test where bottom is the smallest value and top is the largest possible value
        Range range = Range.between(String.valueOf(Byte.MIN_VALUE), false, String.valueOf(Byte.MAX_VALUE), true);
        assertNotNull("Range object should not be null", range);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBetweenEmptyStrings() {
        // Test edge case where bottom and top are empty strings, which should throw an exception
        Range.between("", false, "", false);
    }
}