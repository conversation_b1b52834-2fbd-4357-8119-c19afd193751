package com.google.gson;

// package com.google.gson; // Correct // package declaration

import com.google.gson.JsonArray;
import com.google.gson.JsonNull;
import com.google.gson.JsonPrimitive;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class JsonArrayTest {

    private JsonArray jsonArray;
    private JsonArray jsonArrayToAdd;

    @Before
    public void setUp() {
        jsonArray = new JsonArray();
        jsonArrayToAdd = new JsonArray();
    }

    @Test
    public void testAddAll_EmptySourceArray() {
        jsonArray.addAll(jsonArrayToAdd);

        assertEquals("Array should remain empty when adding empty array", 0, jsonArray.size());
    }

    @Test
    public void testAddAll_NonEmptySourceArray() {
        jsonArrayToAdd.add(new JsonPrimitive("Test"));
        jsonArrayToAdd.add(new JsonPrimitive(42));

        jsonArray.addAll(jsonArrayToAdd);

        assertEquals("Array should have size 2 after adding two elements", 2, jsonArray.size());
        assertTrue("Array should contain the element 'Test'", jsonArray.contains(new JsonPrimitive("Test")));
        assertTrue("Array should contain the element '42'", jsonArray.contains(new JsonPrimitive(42)));
    }

    @Test
    public void testAddAll_MultipleAdds() {
        jsonArrayToAdd.add(new JsonPrimitive("First"));
        jsonArray.addAll(jsonArrayToAdd);

        JsonArray anotherArrayToAdd = new JsonArray();
        anotherArrayToAdd.add(new JsonPrimitive("Second"));
        anotherArrayToAdd.add(new JsonPrimitive("Third"));

        jsonArray.addAll(anotherArrayToAdd);

        assertEquals("Array should have size 3 after adding multiple arrays", 3, jsonArray.size());
        assertTrue("Array should contain the element 'First'", jsonArray.contains(new JsonPrimitive("First")));
        assertTrue("Array should contain the element 'Second'", jsonArray.contains(new JsonPrimitive("Second")));
        assertTrue("Array should contain the element 'Third'", jsonArray.contains(new JsonPrimitive("Third")));
    }

    @Test
    public void testAddAll_SourceArrayWithNulls() {
        jsonArrayToAdd.add(JsonNull.INSTANCE);
        jsonArrayToAdd.add(new JsonPrimitive(15));

        jsonArray.addAll(jsonArrayToAdd);

        assertEquals("Array should have size 2 after adding two elements (one null)", 2, jsonArray.size());
        assertTrue("Array should contain the element '15'", jsonArray.contains(new JsonPrimitive(15)));
        assertTrue("Array should contain JsonNull instance when null element is added", jsonArray.contains(JsonNull.INSTANCE));
    }

    @Test
    public void testAddAll_SourceArrayWithJsonNull() {
        jsonArrayToAdd.add(JsonNull.INSTANCE);
        jsonArrayToAdd.add(new JsonPrimitive(10.5));

        jsonArray.addAll(jsonArrayToAdd);

        assertEquals("Array should have size 2 after adding two elements (one JsonNull)", 2, jsonArray.size());
        assertTrue("Array should contain the element '10.5'", jsonArray.contains(new JsonPrimitive(10.5)));
        assertTrue("Array should contain JsonNull instance", jsonArray.contains(JsonNull.INSTANCE));
    }
}