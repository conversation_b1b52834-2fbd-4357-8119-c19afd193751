package org.jfree.chart.ui;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import org.junit.Test;

public class LengthAdjustmentTypeTest {

    @Test
    public void testHashCode() {
        // Test that two objects with the same name have the same hash code
        LengthAdjustmentType noChange1 = LengthAdjustmentType.NO_CHANGE;
        LengthAdjustmentType noChange2 = LengthAdjustmentType.NO_CHANGE;
        assertEquals("Hashcode should be the same for identical instances", noChange1.hashCode(), noChange2.hashCode());

        LengthAdjustmentType expand1 = LengthAdjustmentType.EXPAND;
        LengthAdjustmentType expand2 = LengthAdjustmentType.EXPAND;
        assertEquals("Hashcode should be the same for identical instances", expand1.hashCode(), expand2.hashCode());

        LengthAdjustmentType contract1 = LengthAdjustmentType.CONTRACT;
        LengthAdjustmentType contract2 = LengthAdjustmentType.CONTRACT;
        assertEquals("Hashcode should be the same for identical instances", contract1.hashCode(), contract2.hashCode());

        // Test that objects with different names have different hash codes
        assertNotEquals("Hashcode should be different for different instances", noChange1.hashCode(), expand1.hashCode());
        assertNotEquals("Hashcode should be different for different instances", noChange1.hashCode(), contract1.hashCode());
        assertNotEquals("Hashcode should be different for different instances", expand1.hashCode(), contract1.hashCode());
    }
}