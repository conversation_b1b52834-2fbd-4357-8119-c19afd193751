package org.apache.commons.cli;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class BuilderTest {

    private Builder builder;

    // Assume Builder is a class with the focal method 'argName' and a getter 'getArgName'
    class Builder {
        private String argName;

        public Builder argName(final String argName) {
            this.argName = argName;
            return this;
        }
        
        public String getArgName() {
            return this.argName;
        }
    }
    
    @Before
    public void setUp() {
        builder = new Builder();
    }

    @Test
    public void testArgNameWithValidString() {
        String testArgName = "testName";
        builder.argName(testArgName);
        assertEquals("The argName should be set to 'testName'", testArgName, builder.getArgName());
    }

    @Test
    public void testArgNameWithEmptyString() {
        String testArgName = "";
        builder.argName(testArgName);
        assertEquals("The argName should be set to an empty string", testArgName, builder.getArgName());
    }

    @Test
    public void testArgNameWithNull() {
        builder.argName(null);
        assertNull("The argName should be set to null", builder.getArgName());
    }
}