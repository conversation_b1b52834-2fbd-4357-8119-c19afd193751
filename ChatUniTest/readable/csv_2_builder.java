package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;

public class CSVFormatTest {

    @Test
    public void testBuilderDefaultFormat() {
        CSVFormat originalFormat = CSVFormat.DEFAULT;
        CSVFormat.Builder builder = originalFormat.builder();
        CSVFormat newFormat = builder.build();

        assertEquals("Delimiter should be equal", originalFormat.getDelimiterString(), newFormat.getDelimiterString());
        assertEquals("Quote character should be equal", originalFormat.getQuoteCharacter(), newFormat.getQuoteCharacter());
        assertEquals("Quote mode should be equal", originalFormat.getQuoteMode(), newFormat.getQuoteMode());
        assertEquals("Comment marker should be equal", originalFormat.getCommentMarker(), newFormat.getCommentMarker());
        assertEquals("Escape character should be equal", originalFormat.getEscapeCharacter(), newFormat.getEscapeCharacter());
        assertEquals("Ignore surrounding spaces should be equal", originalFormat.getIgnoreSurroundingSpaces(), newFormat.getIgnoreSurroundingSpaces());
        assertEquals("Allow missing column names should be equal", originalFormat.getAllowMissingColumnNames(), newFormat.getAllowMissingColumnNames());
        assertEquals("Ignore empty lines should be equal", originalFormat.getIgnoreEmptyLines(), newFormat.getIgnoreEmptyLines());
        assertEquals("Record separator should be equal", originalFormat.getRecordSeparator(), newFormat.getRecordSeparator());
        assertEquals("Null string should be equal", originalFormat.getNullString(), newFormat.getNullString());
        assertArrayEquals("Header comments should be equal", originalFormat.getHeaderComments(), newFormat.getHeaderComments());
        assertArrayEquals("Headers should be equal", originalFormat.getHeader(), newFormat.getHeader());
        assertEquals("Skip header record should be equal", originalFormat.getSkipHeaderRecord(), newFormat.getSkipHeaderRecord());
        assertEquals("Ignore header case should be equal", originalFormat.getIgnoreHeaderCase(), newFormat.getIgnoreHeaderCase());
        assertEquals("Trailing delimiter should be equal", originalFormat.getTrailingDelimiter(), newFormat.getTrailingDelimiter());
        assertEquals("Trim should be equal", originalFormat.getTrim(), newFormat.getTrim());
        assertEquals("Auto flush should be equal", originalFormat.getAutoFlush(), newFormat.getAutoFlush());
        assertEquals("Duplicate header mode should be equal", originalFormat.getDuplicateHeaderMode(), newFormat.getDuplicateHeaderMode());
    }

    @Test
    public void testBuilderExcelFormat() {
        CSVFormat originalFormat = CSVFormat.EXCEL;
        CSVFormat.Builder builder = originalFormat.builder();
        CSVFormat newFormat = builder.build();

        assertEquals("Delimiter should be equal", originalFormat.getDelimiterString(), newFormat.getDelimiterString());
        assertEquals("Quote character should be equal", originalFormat.getQuoteCharacter(), newFormat.getQuoteCharacter());
        assertEquals("Default record separator should be equal", originalFormat.getRecordSeparator(), newFormat.getRecordSeparator());
        assertEquals("Ignore empty lines should be equal", originalFormat.getIgnoreEmptyLines(), newFormat.getIgnoreEmptyLines());
        assertEquals("Allow missing column names should be equal", originalFormat.getAllowMissingColumnNames(), newFormat.getAllowMissingColumnNames());
        assertEquals("Duplicate header mode should be equal", originalFormat.getDuplicateHeaderMode(), newFormat.getDuplicateHeaderMode());
    }

    @Test
    public void testBuilderCustomFormat() {
        CSVFormat originalFormat = CSVFormat.DEFAULT.withQuote('\'').withNullString("NULL");
        CSVFormat.Builder builder = originalFormat.builder();
        CSVFormat newFormat = builder.build();

        assertEquals("Delimiter should be equal", originalFormat.getDelimiterString(), newFormat.getDelimiterString());
        assertEquals("Quote character should be equal", originalFormat.getQuoteCharacter(), newFormat.getQuoteCharacter());
        assertEquals("Null string should be equal", "NULL", newFormat.getNullString());
    }
}