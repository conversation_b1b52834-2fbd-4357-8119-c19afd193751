package com.google.gson;

// package com.google.gson;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonPrimitive;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class JsonArrayTest {

    private JsonArray jsonArray;

    @Before
    public void setUp() {
        jsonArray = new JsonArray();
    }

    @Test
    public void testAddCharacter() {
        // Test adding a non-null character
        char testCharacter = 'A';
        jsonArray.add(testCharacter);
        assertFalse(jsonArray.isEmpty());
        JsonElement element = jsonArray.get(0);
        assertTrue(element instanceof JsonPrimitive);
        assertEquals("A", element.getAsString());

        // Test adding a null character, should replace with JsonNull
        jsonArray.add((Character) null);
        assertEquals(2, jsonArray.size());
        JsonElement nullElement = jsonArray.get(1);
        assertTrue(nullElement instanceof JsonNull);
    }

    @Test
    public void testAddCharacterNullHandling() {
        // Checking the size after adding null character
        jsonArray.add((Character) null);
        assertEquals(1, jsonArray.size());

        // Checking if the added element is JsonNull after adding null character
        JsonElement element = jsonArray.get(0);
        assertTrue(element instanceof JsonNull);
    }

    @Test
    public void testAddMultipleCharactersAndNull() {
        jsonArray.add('X');
        jsonArray.add('Y');
        jsonArray.add((Character) null);

        assertEquals(3, jsonArray.size());

        JsonElement firstElement = jsonArray.get(0);
        assertTrue(firstElement instanceof JsonPrimitive);
        assertEquals("X", firstElement.getAsString());

        JsonElement secondElement = jsonArray.get(1);
        assertTrue(secondElement instanceof JsonPrimitive);
        assertEquals("Y", secondElement.getAsString());

        JsonElement thirdElement = jsonArray.get(2);
        assertTrue(thirdElement instanceof JsonNull);
    }
}