package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Test;
import java.util.Collections;

public class PatternsTest {

    @Test
    public void testAnythingButSuffix_withRegularSuffix() {
        // Arrange
        String suffix = "test";
        
        // Act
        AnythingButValuesSet result = Patterns.anythingButSuffix(suffix);
        
        // Assert
        assertNotNull("Result should not be null", result);
        assertEquals("MatchType should be ANYTHING_BUT_SUFFIX", MatchType.ANYTHING_BUT_SUFFIX, result.type());
        assertEquals("The reversed suffix should match the expected output", 
                      Collections.singleton(new StringBuilder(suffix).reverse().toString()), result.getValues());
    }
    
    @Test
    public void testAnythingButSuffix_withEmptySuffix() {
        // Arrange
        String suffix = "";
        
        // Act
        AnythingButValuesSet result = Patterns.anythingButSuffix(suffix);
        
        // Assert
        assertNotNull("Result should not be null", result);
        assertEquals("MatchType should be ANYTHING_BUT_SUFFIX", MatchType.ANYTHING_BUT_SUFFIX, result.type());
        assertEquals("The empty reversed suffix should match the expected output", Collections.singleton(""), result.getValues());
    }
    
    @Test
    public void testAnythingButSuffix_withSpecialCharacters() {
        // Arrange
        String suffix = "!@#";
        
        // Act
        AnythingButValuesSet result = Patterns.anythingButSuffix(suffix);
        
        // Assert
        assertNotNull("Result should not be null", result);
        assertEquals("MatchType should be ANYTHING_BUT_SUFFIX", MatchType.ANYTHING_BUT_SUFFIX, result.type());
        assertEquals("The reversed suffix with special characters should match the expected output", 
                      Collections.singleton(new StringBuilder(suffix).reverse().toString()), result.getValues());
    }
}