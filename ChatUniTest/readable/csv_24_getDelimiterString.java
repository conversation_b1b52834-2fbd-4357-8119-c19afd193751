package org.apache.commons.csv;

// package org.apache.commons.csv;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

public class CSVFormatTest {

    @Test
    public void testGetDelimiterString_DefaultDelimiter() {
        CSVFormat csvFormat = CSVFormat.DEFAULT;
        assertNotNull(csvFormat.getDelimiterString(), "Delimiter should not be null");
        assertEquals(",", csvFormat.getDelimiterString(), "Delimiter should be a comma");
    }

    @Test
    public void testGetDelimiterString_CustomDelimiter() {
        CSVFormat csvFormat = CSVFormat.newFormat(';');
        assertNotNull(csvFormat.getDelimiterString(), "Delimiter should not be null");
        assertEquals(";", csvFormat.getDelimiterString(), "Delimiter should be a semicolon");
    }

    @Test
    public void testGetDelimiterString_TabDelimiter() {
        CSVFormat csvFormat = CSVFormat.TDF;
        assertNotNull(csvFormat.getDelimiterString(), "Delimiter should not be null");
        assertEquals("\t", csvFormat.getDelimiterString(), "Delimiter should be a tab");
    }

    @Test
    public void testGetDelimiterString_InvalidDelimiter() {
        // Test an invalid delimiter setup
        assertThrows(IllegalArgumentException.class, () -> CSVFormat.newFormat('\n'));
    }

    @Test
    public void testGetDelimiterString_CloneDelimiter() {
        CSVFormat original = CSVFormat.EXCEL;
        CSVFormat clone = original.copy();
        assertEquals(original.getDelimiterString(), clone.getDelimiterString(), "Cloned format should have the same delimiter");
    }

    @Test
    public void testGetDelimiterString_SetDelimiterViaBuilder() {
        CSVFormat csvFormat = CSVFormat.Builder.create().setDelimiter('|').build();
        assertNotNull(csvFormat.getDelimiterString(), "Delimiter should not be null");
        assertEquals("|", csvFormat.getDelimiterString(), "Delimiter should be a pipe");
    }
}