package com.google.gson.internal.bind;

// package com.google.gson.internal.bind;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapterFactory;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class DummyTypeAdapterFactoryTest {

    private static class DummyTypeAdapterFactory implements TypeAdapterFactory {
        @Override
        public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
            throw new AssertionError("Factory should not be used");
        }
    }

    @Test
    public void testCreate_MethodThrowsAssertionError() {
        // Arrange
        DummyTypeAdapterFactory factory = new DummyTypeAdapterFactory();
        Gson gson = new Gson();
        TypeToken<String> typeToken = TypeToken.get(String.class);
        
        // Act & Assert
        assertThrows(AssertionError.class, () -> factory.create(gson, typeToken));
    }
}