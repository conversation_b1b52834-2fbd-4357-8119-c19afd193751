package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class for the Conversion utility methods.
 */
public class ConversionTest {

    @Test(expected = IllegalArgumentException.class)
    public void testBinaryBeMsb0ToHexDigit_EmptyArray() {
        // Test when the input array is empty
        Conversion.binaryBeMsb0ToHexDigit(new boolean[]{});
    }

    @Test(expected = NullPointerException.class)
    public void testBinaryBeMsb0ToHexDigit_NullArray() {
        // Test when the input array is null
        Conversion.binaryBeMsb0ToHexDigit(null);
    }

    @Test
    public void testBinaryBeMsb0ToHexDigit_ValidConversions() {
        // Tests with valid inputs and expected outputs

        // Test conversion of (false, false, false, true) -> '1'
        assertEquals('1', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, false, false, true}));

        // Test conversion of (false, false, true, false) -> '2'
        assertEquals('2', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, false, true, false}));

        // Test conversion of (false, false, true, true) -> '3'
        assertEquals('3', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, false, true, true}));

        // Test conversion of (false, true, false, false) -> '4'
        assertEquals('4', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, true, false, false}));

        // Test conversion of (false, true, false, true) -> '5'
        assertEquals('5', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, true, false, true}));

        // Test conversion of (false, true, true, false) -> '6'
        assertEquals('6', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, true, true, false}));

        // Test conversion of (false, true, true, true) -> '7'
        assertEquals('7', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{false, true, true, true}));

        // Test conversion of (true, false, false, false) -> '8'
        assertEquals('8', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, false, false, false}));

        // Test conversion of (true, false, false, true) -> '9'
        assertEquals('9', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, false, false, true}));

        // Test conversion of (true, false, true, false) -> 'a'
        assertEquals('a', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, false, true, false}));

        // Test conversion of (true, false, true, true) -> 'b'
        assertEquals('b', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, false, true, true}));

        // Test conversion of (true, true, false, false) -> 'c'
        assertEquals('c', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, true, false, false}));

        // Test conversion of (true, true, false, true) -> 'd'
        assertEquals('d', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, true, false, true}));

        // Test conversion of (true, true, true, false) -> 'e'
        assertEquals('e', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, true, true, false}));

        // Test conversion of (true, true, true, true) -> 'f'
        assertEquals('f', Conversion.binaryBeMsb0ToHexDigit(new boolean[]{true, true, true, true}));
    }
}