package org.apache.commons.csv;

// package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.Mockito;

import java.io.Flushable;
import java.io.IOException;

public class CSVPrinterTest {

    class TestFlushableAppendable implements Appendable, Flushable {
        boolean flushed = false;

        @Override
        public Appendable append(CharSequence csq) throws IOException {
            return this;
        }

        @Override
        public Appendable append(CharSequence csq, int start, int end) throws IOException {
            return this;
        }

        @Override
        public Appendable append(char c) throws IOException {
            return this;
        }

        @Override
        public void flush() throws IOException {
            flushed = true;
        }
    }

    @Test
    public void testFlushWithFlushableAppendable() throws IOException {
        TestFlushableAppendable testAppendable = new TestFlushableAppendable();
        CSVFormat format = Mockito.mock(CSVFormat.class);

        CSVPrinter printer = new CSVPrinter(testAppendable, format);
        printer.flush();

        assertTrue("Flush method should have been called", testAppendable.flushed);
    }

    @Test
    public void testFlushWithNonFlushableAppendable() throws IOException {
        Appendable nonFlushableAppendable = Mockito.mock(Appendable.class);
        CSVFormat format = Mockito.mock(CSVFormat.class);

        CSVPrinter printer = new CSVPrinter(nonFlushableAppendable, format);
        printer.flush();

        // Since nonFlushableAppendable is not Flushable, no flush should be called.
        // No exception should be thrown and no state change should be there to check;
        // So if execution reaches here, the test is successful
    }

    @Test
    public void testFlushIOException() throws IOException {
        // Use a combined mock for Appendable and Flushable
        class AppendableFlushableMock implements Appendable, Flushable {
            @Override
            public Appendable append(CharSequence csq) throws IOException {
                return this;
            }

            @Override
            public Appendable append(CharSequence csq, int start, int end) throws IOException {
                return this;
            }

            @Override
            public Appendable append(char c) throws IOException {
                return this;
            }

            @Override
            public void flush() throws IOException {
                throw new IOException("Flush error");
            }
        }

        AppendableFlushableMock appendableFlushableMock = Mockito.spy(new AppendableFlushableMock());
        CSVFormat format = Mockito.mock(CSVFormat.class);

        // Mock the flush method to throw IOException
        Mockito.doThrow(new IOException("Flush error")).when(appendableFlushableMock).flush();

        CSVPrinter printer = new CSVPrinter(appendableFlushableMock, format);

        try {
            printer.flush();
            fail("IOException should have been thrown");
        } catch (IOException e) {
            assertEquals("Flush error", e.getMessage());
        }
    }
}