package org.jfree.chart.ui;

// package org.jfree.chart.ui;  // Ensure this matches with the // package of RectangleInsets.java

import org.jfree.chart.util.UnitType;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

public class RectangleInsetsTest {

    @Test
    public void testToStringWithAbsoluteUnits() {
        RectangleInsets insets = new RectangleInsets(UnitType.ABSOLUTE, 5.0, 10.0, 15.0, 20.0);
        assertEquals("RectangleInsets[t=5.0,l=10.0,b=15.0,r=20.0]", insets.toString());
    }
    
    @Test
    public void testToStringWithDefaultConstructor() {
        RectangleInsets insets = new RectangleInsets();
        assertEquals("RectangleInsets[t=1.0,l=1.0,b=1.0,r=1.0]", insets.toString());
    }
    
    @Test
    public void testToStringWithZeroInsets() {
        RectangleInsets insets = RectangleInsets.ZERO_INSETS;
        assertEquals("RectangleInsets[t=0.0,l=0.0,b=0.0,r=0.0]", insets.toString());
    }

    @Test
    public void testToStringWithRelativeUnits() {
        RectangleInsets insets = new RectangleInsets(UnitType.RELATIVE, 0.1, 0.2, 0.3, 0.4);
        assertEquals("RectangleInsets[t=0.1,l=0.2,b=0.3,r=0.4]", insets.toString());
    }
}