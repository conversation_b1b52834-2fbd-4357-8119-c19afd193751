package org.apache.commons.cli;

// package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.assertEquals;

import java.io.Serializable;
import java.util.Comparator;

public class OptionComparatorTest {

    private final OptionComparator comparator = new OptionComparator();

    // Mock Option class for testing purposes
    private static class Option {
        private final String key;
        
        public Option(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    }

    // Inner class for OptionComparator
    private static final class OptionComparator implements Comparator<Option>, Serializable {
        private static final long serialVersionUID = 5305467873966684014L;

        @Override
        public int compare(final Option opt1, final Option opt2) {
            return opt1.getKey().compareToIgnoreCase(opt2.getKey());
        }
    }
    
    @Test
    public void testCompare_LessThan() {
        Option opt1 = new Option("apple");
        Option opt2 = new Option("banana");
        int result = comparator.compare(opt1, opt2);
        
        // Assert that the result is negative since "apple" is less than "banana"
        assertEquals(true, result < 0);
    }
    
    @Test
    public void testCompare_Equal() {
        Option opt1 = new Option("cherry");
        Option opt2 = new Option("Cherry");
        int result = comparator.compare(opt1, opt2);
        
        // Assert that the result is zero since "cherry" and "Cherry" are considered equal (ignoring case)
        assertEquals(0, result);
    }
    
    @Test
    public void testCompare_GreaterThan() {
        Option opt1 = new Option("date");
        Option opt2 = new Option("apricot");
        int result = comparator.compare(opt1, opt2);
        
        // Assert that the result is positive since "date" is greater than "apricot"
        assertEquals(true, result > 0);
    }

    @Test
    public void testCompare_SpecialCharacters() {
        Option opt1 = new Option("#hash");
        Option opt2 = new Option("hash");
        int result = comparator.compare(opt1, opt2);
        
        // Assert that the result is negative since "#hash" comes before "hash"
        assertEquals(true, result < 0);
    }
    
    @Test
    public void testCompare_Numbers() {
        Option opt1 = new Option("9");
        Option opt2 = new Option("10");
        int result = comparator.compare(opt1, opt2);
        
        // Assert that the result is positive since "9" comes after "10" lexicographically
        assertEquals(true, result > 0); // Corrected assertion
    }
}