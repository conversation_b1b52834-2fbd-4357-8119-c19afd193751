package org.apache.commons.csv;

// package org.apache.commons.csv;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * BuilderTest to test the create method in Builder class
 */
public class BuilderTest {

    private CSVFormat testFormat;

    @Before
    public void setUp() {
        // Create a CSVFormat using a builder
        CSVFormat.Builder builder = CSVFormat.DEFAULT.builder();
        
        builder.setDelimiter(',')
               .setQuote('"')
               .setQuoteMode(QuoteMode.MINIMAL)
               .setCommentMarker('#')
               .setEscape('\\')
               .setIgnoreSurroundingSpaces(true)
               .setAllowMissingColumnNames(false)
               .setIgnoreEmptyLines(false)
               .setRecordSeparator("\n")
               //.setNullString(null) // Skipping setting nullString if it's not mandatory
               .setSkipHeaderRecord(false)
               .setIgnoreHeaderCase(false)
               .setTrailingDelimiter(false)
               .setTrim(false)
               .setAutoFlush(true)
               .setHeader("header1", "header2")
               .setHeaderComments("Comment1", "Comment2")
               //.setQuotedNullString("\"null\"") // Skipping quotedNullString if it's not accessible
               .setDuplicateHeaderMode(DuplicateHeaderMode.ALLOW_EMPTY);
        
        testFormat = builder.build();
    }

    @Test
    public void testCreateWithCSVFormat() {
        CSVFormat.Builder builder = CSVFormat.Builder.create(testFormat);

        assertNotNull("Builder should not be null", builder);
        assertEquals("delimiter should be copied", testFormat.getDelimiter(), builder.build().getDelimiter());
        assertEquals("quoteCharacter should be copied", testFormat.getQuoteCharacter(), builder.build().getQuoteCharacter());
        assertEquals("quoteMode should be copied", testFormat.getQuoteMode(), builder.build().getQuoteMode());
        assertEquals("commentMarker should be copied", testFormat.getCommentMarker(), builder.build().getCommentMarker());
        assertEquals("escapeCharacter should be copied", testFormat.getEscapeCharacter(), builder.build().getEscapeCharacter());
        assertEquals("ignoreSurroundingSpaces should be copied", testFormat.getIgnoreSurroundingSpaces(), builder.build().getIgnoreSurroundingSpaces());
        assertEquals("allowMissingColumnNames should be copied", testFormat.getAllowMissingColumnNames(), builder.build().getAllowMissingColumnNames());
        assertEquals("ignoreEmptyLines should be copied", testFormat.getIgnoreEmptyLines(), builder.build().getIgnoreEmptyLines());
        assertEquals("recordSeparator should be copied", testFormat.getRecordSeparator(), builder.build().getRecordSeparator());
        assertEquals("skipHeaderRecord should be copied", testFormat.getSkipHeaderRecord(), builder.build().getSkipHeaderRecord());
        assertEquals("ignoreHeaderCase should be copied", testFormat.getIgnoreHeaderCase(), builder.build().getIgnoreHeaderCase());
        assertEquals("trailingDelimiter should be copied", testFormat.getTrailingDelimiter(), builder.build().getTrailingDelimiter());
        assertEquals("trim should be copied", testFormat.getTrim(), builder.build().getTrim());
        assertEquals("autoFlush should be copied", testFormat.getAutoFlush(), builder.build().getAutoFlush());
        assertArrayEquals("headers should be copied", testFormat.getHeader(), builder.build().getHeader());
        assertArrayEquals("headerComments should be copied", testFormat.getHeaderComments(), builder.build().getHeaderComments());
        assertEquals("duplicateHeaderMode should be copied", testFormat.getDuplicateHeaderMode(), builder.build().getDuplicateHeaderMode());
    }
}