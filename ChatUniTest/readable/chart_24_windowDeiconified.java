package org.jfree.chart.ui;

import static org.junit.Assert.*;
import org.junit.Test;

import java.awt.event.WindowEvent;
import javax.swing.JFrame;

public class ApplicationFrameTest {

    @Test
    public void testWindowDeiconified() {
        // Create an instance of ApplicationFrame with a test title
        ApplicationFrame frame = new ApplicationFrame("Test Frame");

        // Simulate the windowDeiconified event
        WindowEvent event = new WindowEvent(frame, WindowEvent.WINDOW_DEICONIFIED);

        // Call the windowDeiconified method
        frame.windowDeiconified(event);

        // Assertions to ensure that the frame is still visible and not disposed
        assertFalse(frame.isDisplayable()); // Since no action is taken, frame should still be visible
        assertEquals(JFrame.NORMAL, frame.getExtendedState()); // State should still be normal

        // Clean up
        frame.dispose();
    }
}