package com.google.gson.internal.bind;

// package com.google.gson.internal.bind;

import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import org.junit.Before;
import org.junit.Test;
import java.io.IOException;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

// Ensure necessary import statements
public class JsonTreeReaderTest {

    private JsonTreeReader reader;

    @Before
    public void setUp() {
        String json = "[{\"key\": \"value\"}, 1, null, true, 2.5]";
        JsonElement element = JsonParser.parseString(json);
        reader = new JsonTreeReader(element);
    }

    @Test
    public void testClose() throws IOException {
        // Ensure that reader can perform operations before close
        assertTrue(reader.hasNext());

        // Close the reader
        reader.close();

        // Assertions after closure
        try {
            reader.peek();
            fail("Expected IllegalStateException after close");
        } catch (IllegalStateException e) {
            assertEquals("JsonReader is closed", e.getMessage());
        }

        try {
            reader.beginArray();
            fail("Expected IllegalStateException after close");
        } catch (IllegalStateException e) {
            assertEquals("JsonReader is closed", e.getMessage());
        }

        try {
            reader.endArray();
            fail("Expected IllegalStateException after close");
        } catch (IllegalStateException e) {
            assertEquals("JsonReader is closed", e.getMessage());
        }

        // Since we can't access the private stackSize directly,
        // we assume the reader is correctly closed based on behavior
        // This assumes `peek` and other operations verify state internally.
    }
}