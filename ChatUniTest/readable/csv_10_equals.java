package org.apache.commons.csv;

// package org.apache.commons.csv;

import org.junit.Test;
import static org.junit.Assert.*;

public class CSVFormatTest {

    @Test
    public void testEquals_SameInstance() {
        CSVFormat format = CSVFormat.DEFAULT;
        assertTrue(format.equals(format));
    }

    @Test
    public void testEquals_DifferentObjectType() {
        CSVFormat format = CSVFormat.DEFAULT;
        String differentTypeObject = "NotACSVFormat";
        assertFalse(format.equals(differentTypeObject));
    }

    @Test
    public void testEquals_Null() {
        CSVFormat format = CSVFormat.DEFAULT;
        assertFalse(format.equals(null));
    }

    @Test
    public void testEquals_EqualFormats() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = CSVFormat.DEFAULT;
        assertTrue(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentDuplicateHeaderMode() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setDuplicateHeaderMode(DuplicateHeaderMode.ALLOW_EMPTY).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentAllowMissingColumnNames() {
        CSVFormat format1 = CSVFormat.EXCEL; // This uses 'true' for allowMissingColumnNames
        CSVFormat format2 = format1.builder().setAllowMissingColumnNames(false).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentAutoFlush() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setAutoFlush(true).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentCommentMarker() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setCommentMarker('#').build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentDelimiter() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setDelimiter(';').build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentEscapeCharacter() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setEscape('!').build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentHeaders() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setHeader("Column1", "Column2").build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentHeaderComments() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setHeaderComments("Comment1", "Comment2").build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentIgnoreEmptyLines() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setIgnoreEmptyLines(false).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentIgnoreHeaderCase() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setIgnoreHeaderCase(true).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentIgnoreSurroundingSpaces() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setIgnoreSurroundingSpaces(true).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentNullString() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setNullString("NULL").build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentQuoteCharacter() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setQuote('\'').build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentQuoteMode() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setQuoteMode(QuoteMode.ALL).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentQuotedNullString() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setNullString("NULL").build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentRecordSeparator() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setRecordSeparator('\n').build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentSkipHeaderRecord() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setSkipHeaderRecord(true).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentTrailingDelimiter() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setTrailingDelimiter(true).build();
        assertFalse(format1.equals(format2));
    }

    @Test
    public void testEquals_DifferentTrim() {
        CSVFormat format1 = CSVFormat.DEFAULT;
        CSVFormat format2 = format1.builder().setTrim(true).build();
        assertFalse(format1.equals(format2));
    }
}