package org.apache.commons.csv;

// package org.apache.commons.csv;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import java.io.StringReader;

public class CSVRecordTest {

    private CSVParser mockParser;
    private String[] sampleValues;
    private CSVRecord csvRecord;

    @Before
    public void setUp() throws Exception {
        // Set up a basic CSVFormat for test
        CSVFormat format = CSVFormat.DEFAULT;

        // Use a StringReader with some dummy data to instantiate the parser
        mockParser = new CSVParser(new StringReader("dummy"), format);

        // Sample values for CSV record
        sampleValues = new String[]{"value1", "value2", "value3"};
        
        // Create instance of CSVRecord with sample data
        csvRecord = new CSVRecord(mockParser, sampleValues, null, 1, 0);
    }

    @Test
    public void testGet_indexWithinBounds_shouldReturnCorrectValue() {
        // Test index 0
        String value0 = csvRecord.get(0);
        assertEquals("value1", value0);

        // Test index 1
        String value1 = csvRecord.get(1);
        assertEquals("value2", value1);

        // Test index 2
        String value2 = csvRecord.get(2);
        assertEquals("value3", value2);
    }

    @Test
    public void testGet_indexOutOfBounds_shouldThrowArrayIndexOutOfBoundsException() {
        // Test index out of lower bound
        try {
            csvRecord.get(-1);
            fail("Expected ArrayIndexOutOfBoundsException for index -1");
        } catch (ArrayIndexOutOfBoundsException e) {
            // Expected exception
        }

        // Test index out of upper bound
        try {
            csvRecord.get(3);
            fail("Expected ArrayIndexOutOfBoundsException for index 3");
        } catch (ArrayIndexOutOfBoundsException e) {
            // Expected exception
        }
    }
}