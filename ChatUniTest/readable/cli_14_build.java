package org.apache.commons.cli;

import org.junit.Test;
import static org.junit.Assert.*;

public class BuilderTest {

    class Builder {
        private String option;
        private String longOption;

        public Builder() {
        }
        
        public Builder setOption(String option) {
            this.option = option;
            return this;
        }

        public Builder setLongOption(String longOption) {
            this.longOption = longOption;
            return this;
        }

        public Option build() {
            if ((option == null) && (longOption == null)) {
                throw new IllegalArgumentException("Either opt or longOpt must be specified");
            }
            return new Option(this);
        }
    }

    class Option {
        private String option;
        private String longOption;

        public Option(Builder builder) {
            this.option = builder.option;
            this.longOption = builder.longOption;
        }

        public String getOption() {
            return option;
        }

        public String getLongOption() {
            return longOption;
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBuild_throwsExceptionWhenNeitherOptNorLongOptSet() {
        Builder builder = new Builder();
        builder.build(); // Expect IllegalArgumentException
    }

    @Test
    public void testBuild_createsOptionWithOptSet() {
        String expectedOption = "o";
        Builder builder = new Builder().setOption(expectedOption);
        Option option = builder.build();

        assertNotNull("Option object should not be null", option);
        assertEquals("Option should have the expected 'opt' value", expectedOption, option.getOption());
        assertNull("Option 'longOpt' value should be null", option.getLongOption());
    }

    @Test
    public void testBuild_createsOptionWithLongOptSet() {
        String expectedLongOption = "longOpt";
        Builder builder = new Builder().setLongOption(expectedLongOption);
        Option option = builder.build();

        assertNotNull("Option object should not be null", option);
        assertNull("Option 'opt' value should be null", option.getOption());
        assertEquals("Option should have the expected 'longOpt' value", expectedLongOption, option.getLongOption());
    }

    @Test
    public void testBuild_createsOptionWithBothOptAndLongOptSet() {
        String expectedOption = "o";
        String expectedLongOption = "longOpt";
        Builder builder = new Builder().setOption(expectedOption).setLongOption(expectedLongOption);
        Option option = builder.build();

        assertNotNull("Option object should not be null", option);
        assertEquals("Option should have the expected 'opt' value", expectedOption, option.getOption());
        assertEquals("Option should have the expected 'longOpt' value", expectedLongOption, option.getLongOption());
    }
}