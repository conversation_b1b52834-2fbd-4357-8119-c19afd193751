package com.google.gson.internal;

// package com.google.gson.internal;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Iterator;
import java.util.Set;

public class KeySetTest {

    private LinkedTreeMap<String, String> map;
    private Set<String> keySet;

    @Before
    public void setUp() {
        map = new LinkedTreeMap<>();
        keySet = map.keySet();
        
        // Adding sample data to the LinkedTreeMap
        map.put("key1", "value1");
        map.put("key2", "value2");
        map.put("key3", "value3");
    }

    @Test
    public void testClear() {
        // Ensure the keySet is not empty before clear
        assertFalse(keySet.isEmpty());
        
        // Perform the clear operation
        keySet.clear();
        
        // After clearing, the size should be 0
        assertEquals(0, keySet.size());

        // Verify that the keySet iterator has no elements
        Iterator<String> iterator = keySet.iterator();
        assertFalse(iterator.hasNext());

        // Check if the internal map is cleared as well
        assertTrue(map.isEmpty());
    }

    @Test
    public void testClearWhenAlreadyEmpty() {
        // First clear the map to ensure it's empty
        keySet.clear();
        
        // Ensure the keySet is empty now
        assertTrue(keySet.isEmpty());

        // Clear again
        keySet.clear();

        // Assert that clearing again doesn't throw any exception and map is still empty
        assertEquals(0, keySet.size());
        assertTrue(map.isEmpty());
    }
}