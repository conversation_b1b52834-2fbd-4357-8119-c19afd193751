package org.apache.commons.cli;

// package org.apache.commons.cli;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;

// Assuming that the commandLine field in Builder class is some class that has addArg and getArgs methods
class CommandLine {
    private final List<String> args = new ArrayList<>();

    public void addArg(String arg) {
        args.add(arg);
    }

    public List<String> getArgs() {
        return args;
    }
}

// Assuming Builder is a class that contains the focal method
class Builder {
    private final CommandLine commandLine = new CommandLine();
    
    /**
     * Adds left-over unrecognized option/argument.
     *
     * @param arg
     *          the unrecognized option/argument.
     * @return this Builder instance for method chaining.
     */
    public Builder addArg(final String arg) {
        commandLine.addArg(arg);
        return this;
    }
    
    public List<String> getCommandLineArgs() {
        return commandLine.getArgs();
    }
}

public class BuilderTest {

    private Builder builder;

    @Before
    public void setUp() {
        builder = new Builder();
    }
    
    @Test
    public void testAddArgWithValidArg() {
        String arg = "testArg";

        // Chain the method call as per method chaining requirement
        Builder result = builder.addArg(arg);

        // Check that the returned object is the same instance (method chaining)
        assertSame(builder, result);

        // Assert that the argument was added correctly
        List<String> args = builder.getCommandLineArgs();
        assertNotNull(args);
        assertEquals(1, args.size());
        assertEquals(arg, args.get(0));
    }

    @Test
    public void testAddArgWithMultipleArgs() {
        String arg1 = "arg1";
        String arg2 = "arg2";
        
        builder.addArg(arg1).addArg(arg2);

        // Validate the added arguments
        List<String> args = builder.getCommandLineArgs();
        assertNotNull(args);
        assertEquals(2, args.size());
        assertEquals(arg1, args.get(0));
        assertEquals(arg2, args.get(1));
    }

    @Test
    public void testAddArgWithEmptyArg() {
        String emptyArg = "";

        builder.addArg(emptyArg);

        List<String> args = builder.getCommandLineArgs();
        assertNotNull(args);
        assertEquals(1, args.size());
        assertEquals(emptyArg, args.get(0));
    }
    
    @Test
    public void testAddArgWithNullArg() {
        try {
            builder.addArg(null);
            // Depending on the expected behavior of addArg with null value
            // We should either verify if null is added successfully or expect an exception to be thrown.
            // Here assuming null value is acceptable   
            List<String> args = builder.getCommandLineArgs();
            assertNotNull(args);
            assertEquals(1, args.size());
            assertEquals(null, args.get(0));
        } catch (Exception e) {
            // If exception is expected, validate the exception here
            assertEquals(NullPointerException.class, e.getClass());
        }
    }
}