package com.google.gson;

// package com.google.gson;

import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertEquals;

public class JsonObjectTest {

    private JsonObject jsonObject;

    @Before
    public void setUp() {
        jsonObject = new JsonObject();
    }

    @Test
    public void testAddPropertyWithValidCharacter() {
        String property = "charKey";
        Character value = 'A';

        jsonObject.addProperty(property, value);

        JsonElement element = jsonObject.get(property);
        assertTrue(element.isJsonPrimitive());
        assertEquals(new JsonPrimitive(value), element);
    }

    @Test
    public void testAddPropertyWithNullCharacter() {
        String property = "nullCharKey";

        // Explicitly cast null to Character to resolve ambiguity
        jsonObject.addProperty(property, (Character) null);

        JsonElement element = jsonObject.get(property);
        assertTrue(element.isJsonNull());
        assertEquals(JsonNull.INSTANCE, element);
    }

    @Test
    public void testAddPropertyWithEmptyCharacter() {
        String property = "emptyCharKey";
        Character value = '\0';  // Null character

        jsonObject.addProperty(property, value);

        JsonElement element = jsonObject.get(property);
        assertTrue(element.isJsonPrimitive());
        assertEquals(new JsonPrimitive(value), element);
    }

    @Test
    public void testAddMultipleProperties() {
        String property1 = "charKey1";
        Character value1 = 'A';
        String property2 = "charKey2";
        Character value2 = 'B';

        jsonObject.addProperty(property1, value1);
        jsonObject.addProperty(property2, value2);

        JsonElement element1 = jsonObject.get(property1);
        JsonElement element2 = jsonObject.get(property2);

        assertTrue(element1.isJsonPrimitive());
        assertEquals(new JsonPrimitive(value1), element1);

        assertTrue(element2.isJsonPrimitive());
        assertEquals(new JsonPrimitive(value2), element2);
    }

    @Test
    public void testOverwriteExistingProperty() {
        String property = "charKey";
        Character initialValue = 'A';
        Character newValue = 'B';

        jsonObject.addProperty(property, initialValue);
        jsonObject.addProperty(property, newValue);

        JsonElement element = jsonObject.get(property);
        assertTrue(element.isJsonPrimitive());
        assertEquals(new JsonPrimitive(newValue), element);
    }
}