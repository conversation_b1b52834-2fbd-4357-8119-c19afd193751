package com.google.gson;

// package com.google.gson;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Before;
import org.junit.Test;

import java.util.Map;

/**
 * Unit tests for the JsonObject class focusing on the asMap method.
 */
public class JsonObjectTest {
    private JsonObject jsonObject;

    @Before
    public void setUp() {
        jsonObject = new JsonObject();
    }

    @Test
    public void testAsMap_emptyJsonObject() {
        Map<String, JsonElement> map = jsonObject.asMap();
        assertNotNull("Map should not be null", map);
        assertTrue("Map should be empty", map.isEmpty());
    }

    @Test
    public void testAsMap_withAddedProperties() {
        jsonObject.addProperty("name", "John");
        jsonObject.addProperty("age", 30);

        Map<String, JsonElement> map = jsonObject.asMap();
        assertEquals("Map size should be 2", 2, map.size());
        assertTrue("Map should contain the key 'name'", map.containsKey("name"));
        assertEquals("Map value for key 'name' should be 'John'", new JsonPrimitive("John"), map.get("name"));
        assertTrue("Map should contain the key 'age'", map.containsKey("age"));
        assertEquals("Map value for key 'age' should be 30", new JsonPrimitive(30), map.get("age"));
    }

    @Test
    public void testAsMap_modificationReflectsInJsonObject() {
        Map<String, JsonElement> map = jsonObject.asMap();
        map.put("city", new JsonPrimitive("London"));

        assertTrue("JsonObject should have member 'city'", jsonObject.has("city"));
        assertEquals("JsonObject member 'city' should be 'London'", new JsonPrimitive("London"), jsonObject.get("city"));
    }

    @Test
    public void testAsMap_removalReflectsInJsonObject() {
        jsonObject.addProperty("country", "UK");

        Map<String, JsonElement> map = jsonObject.asMap();
        map.remove("country");

        assertFalse("JsonObject should not have member 'country'", jsonObject.has("country"));
    }

    @Test
    public void testAsMap_nullKeyThrowsException() {
        Map<String, JsonElement> map = jsonObject.asMap();
        try {
            map.put(null, new JsonPrimitive("Invalid"));
            fail("Expected NullPointerException for null key");
        } catch (NullPointerException e) {
            // Expected exception, test will pass
        }
    }

    @Test
    public void testAsMap_nullValueThrowsException() {
        Map<String, JsonElement> map = jsonObject.asMap();
        try {
            map.put("someKey", null);
            fail("Expected NullPointerException for null value");
        } catch (NullPointerException e) {
            // Expected exception, test will pass
        }
    }

    @Test
    public void testAsMap_withJsonNull() {
        jsonObject.add("nullValue", JsonNull.INSTANCE);

        Map<String, JsonElement> map = jsonObject.asMap();
        assertEquals("Map value for key 'nullValue' should be JsonNull", JsonNull.INSTANCE, map.get("nullValue"));
    }
}