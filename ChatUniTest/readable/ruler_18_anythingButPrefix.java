package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import static org.junit.Assert.*;

// Assume the necessary classes (AnythingButValuesSet, MatchType) are imported or defined somewhere in the codebase.

public class PatternsTest {

    @Test
    public void testAnythingButPrefixWithSingleValue() {
        // Arrange
        Set<String> inputSet = Collections.singleton("testPrefix");
        
        // Act
        AnythingButValuesSet result = Patterns.anythingButPrefix(inputSet);

        // Assert
        assertNotNull(result);
        assertEquals(MatchType.ANYTHING_BUT_PREFIX, result.type()); // assuming `type()` method exists
        assertTrue(result.getValues().contains("testPrefix"));
        assertEquals(1, result.getValues().size());
    }

    @Test
    public void testAnythingButPrefixWithMultipleValues() {
        // Arrange
        Set<String> inputSet = new HashSet<>();
        inputSet.add("prefix1");
        inputSet.add("prefix2");
        inputSet.add("prefix3");
        
        // Act
        AnythingButValuesSet result = Patterns.anythingButPrefix(inputSet);

        // Assert
        assertNotNull(result);
        assertEquals(MatchType.ANYTHING_BUT_PREFIX, result.type()); // assuming `type()` method exists
        assertTrue(result.getValues().contains("prefix1"));
        assertTrue(result.getValues().contains("prefix2"));
        assertTrue(result.getValues().contains("prefix3"));
        assertEquals(3, result.getValues().size());
    }

    @Test
    public void testAnythingButPrefixWithEmptySet() {
        // Arrange
        Set<String> inputSet = Collections.emptySet();

        // Act
        AnythingButValuesSet result = Patterns.anythingButPrefix(inputSet);

        // Assert
        assertNotNull(result);
        assertEquals(MatchType.ANYTHING_BUT_PREFIX, result.type()); // assuming `type()` method exists
        assertTrue(result.getValues().isEmpty());
    }

    @Test(expected = NullPointerException.class)
    public void testAnythingButPrefixWithNullSet() {
        // Arrange and Act
        Patterns.anythingButPrefix((Set<String>) null);
    }
}