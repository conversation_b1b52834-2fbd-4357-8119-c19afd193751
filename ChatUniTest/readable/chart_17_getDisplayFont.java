package org.jfree.chart.ui;

// package org.jfree.chart.ui;

import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;
import java.awt.Font;
import org.jfree.chart.ui.FontDisplayField;

public class FontDisplayFieldTest {

    private FontDisplayField fontDisplayField;
    private Font testFont;

    @Before
    public void setUp() {
        testFont = new Font("Arial", Font.PLAIN, 12);
        fontDisplayField = new FontDisplayField(testFont);
    }

    @Test
    public void testGetDisplayFontWhenInitialized() {
        Font result = fontDisplayField.getDisplayFont();
        assertNotNull("The display font should not be null", result);
        assertEquals("The font name should be Arial", testFont.getFontName(), result.getFontName());
        assertEquals("The font style should be PLAIN", testFont.getStyle(), result.getStyle());
        assertEquals("The font size should be 12", testFont.getSize(), result.getSize());
    }

    @Test
    public void testGetDisplayFontAfterModification() {
        Font newFont = new Font("Courier New", Font.BOLD, 14);
        fontDisplayField.setDisplayFont(newFont);

        Font result = fontDisplayField.getDisplayFont();
        assertNotNull("The display font should not be null after modification", result);
        assertEquals("The font name should be Courier New", newFont.getFontName(), result.getFontName());
        assertEquals("The font style should be BOLD", newFont.getStyle(), result.getStyle());
        assertEquals("The font size should be 14", newFont.getSize(), result.getSize());
    }

    @Test
    public void testGetDisplayFontWhenFontIsNull() {
        fontDisplayField.setDisplayFont(null);
        
        assertNull("The display font should be null", fontDisplayField.getDisplayFont());
        
        String expectedText = FontDisplayField.localizationResources.getString("No_Font_Selected");
        assertEquals("The display text should indicate no font selected", 
                expectedText, fontDisplayField.getText());
    }
}