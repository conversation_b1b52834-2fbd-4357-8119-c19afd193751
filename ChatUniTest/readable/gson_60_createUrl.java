package com.google.gson.internal;

// package com.google.gson.internal;

import org.junit.Test;
import org.junit.Assert;

public class TroubleshootingGuideTest {

    @Test
    public void testCreateUrlWithValidId() {
        // Arrange
        String id = "section1";
        
        // Act
        String url = TroubleshootingGuide.createUrl(id);

        // Assert
        Assert.assertEquals("The URL should correctly append the id.", 
                     "https://github.com/google/gson/blob/main/Troubleshooting.md#section1", url);
    }

    @Test
    public void testCreateUrlWithEmptyId() {
        // Arrange
        String id = "";

        // Act
        String url = TroubleshootingGuide.createUrl(id);

        // Assert
        Assert.assertEquals("The URL should end with the hash symbol followed by nothing when id is empty.", 
                     "https://github.com/google/gson/blob/main/Troubleshooting.md#", url);
    }

    @Test
    public void testCreateUrlWithSpecialCharacters() {
        // Arrange
        String id = "section#123";

        // Act
        String url = TroubleshootingGuide.createUrl(id);

        // Assert
        Assert.assertEquals("The URL should correctly include special characters in the id.", 
                     "https://github.com/google/gson/blob/main/Troubleshooting.md#section#123", url);
    }

    @Test
    public void testCreateUrlWithNullId() {
        // Arrange
        String id = null;

        // Act
        String url = TroubleshootingGuide.createUrl(id);

        // Assert
        Assert.assertEquals("When id is null, it should result in 'null' being part of the URL.", 
                     "https://github.com/google/gson/blob/main/Troubleshooting.md#null", url);
    }
}