package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import java.util.Collections;
import static org.junit.Assert.*;

public class PatternsTest {

    @Test
    public void testAnythingButWildcard() {
        // Test with a simple string
        String testValue = "test";
        AnythingButValuesSet result = Patterns.anythingButWildcard(testValue);

        // Assertions to verify the behavior of the focal method
        assertNotNull("Result should not be null", result);
        // Assuming 'type()' is the correct method to call
        assertEquals("Match type should be ANYTHING_BUT_WILDCARD", MatchType.ANYTHING_BUT_WILDCARD, result.type());
        assertEquals("The values set should contain the given test value", Collections.singleton(testValue), result.getValues());

        // Test edge cases such as empty strings or special characters
        String emptyValue = "";
        AnythingButValuesSet emptyResult = Patterns.anythingButWildcard(emptyValue);

        assertNotNull("Result for empty string should not be null", emptyResult);
        assertEquals("Match type for empty string should be ANYTHING_BUT_WILDCARD", MatchType.ANYTHING_BUT_WILDCARD, emptyResult.type());
        assertEquals("Value set for empty string should contain the empty value", Collections.singleton(emptyValue), emptyResult.getValues());

        String specialCharValue = "!*&^%";
        AnythingButValuesSet specialResult = Patterns.anythingButWildcard(specialCharValue);

        assertNotNull("Result for special characters should not be null", specialResult);
        assertEquals("Match type for special characters should be ANYTHING_BUT_WILDCARD", MatchType.ANYTHING_BUT_WILDCARD, specialResult.type());
        assertEquals("Value set for special characters should contain the correct value", Collections.singleton(specialCharValue), specialResult.getValues());
    }
}