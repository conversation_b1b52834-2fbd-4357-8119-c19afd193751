package org.apache.commons.csv;

// package org.apache.commons.csv;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;

public class ExtendedBufferedReaderTest {
    private ExtendedBufferedReader extendedBufferedReader;

    @Before
    public void setUp() {
        // Sample input for the reader.
        StringReader stringReader = new StringReader("Sample text\nAnother line\r\nFinal Line");
        // Initialize ExtendedBufferedReader with a StringReader
        extendedBufferedReader = new ExtendedBufferedReader(stringReader);
    }

    @After
    public void tearDown() throws IOException {
        if (!extendedBufferedReader.isClosed()) {
            extendedBufferedReader.close();
        }
    }

    @Test
    public void testClose() throws IOException {
        // Close the ExtendedBufferedReader
        extendedBufferedReader.close();
        
        // Assert that 'closed' flag is set to true
        assertTrue("The reader should be marked as closed.", extendedBufferedReader.isClosed());

        // Assert that 'lastChar' is set to END_OF_STREAM after closing
        assertEquals("Last character should be set to END_OF_STREAM", Constants.END_OF_STREAM, extendedBufferedReader.getLastChar());
        
        // Attempting to close an already closed reader should not throw an exception
        try {
            extendedBufferedReader.close();
        } catch (IOException e) {
            fail("Closing an already closed reader should not throw an exception.");
        }
    }

    @Test(expected = IOException.class)
    public void testCloseThrowsIOException() throws IOException {
        // Create a mock reader to throw IOException on close
        BufferedReader mockReader = new BufferedReader(new Reader() {
            @Override
            public int read(char[] cbuf, int off, int len) throws IOException {
                throw new UnsupportedOperationException();
            }

            @Override
            public void close() throws IOException {
                throw new IOException("Mock IOException");
            }
        });

        ExtendedBufferedReader readerWithException = new ExtendedBufferedReader(mockReader);
        // Expect IOException when closing the mock reader
        readerWithException.close();
    }
}