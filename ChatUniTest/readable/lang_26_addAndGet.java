package org.apache.commons.lang3.mutable;

import org.junit.Assert;
import org.junit.Test;

public class MutableLongTest {

    @Test
    public void testAddAndGetWithZero() {
        MutableLong mutableLong = new MutableLong(10L);
        long result = mutableLong.addAndGet(0L);
        Assert.assertEquals(10L, result);
        Assert.assertEquals(10L, mutableLong.getValue().longValue());
    }

    @Test
    public void testAddAndGetWithPositiveOperand() {
        MutableLong mutableLong = new MutableLong(10L);
        long result = mutableLong.addAndGet(5L);
        Assert.assertEquals(15L, result);
        Assert.assertEquals(15L, mutableLong.getValue().longValue());
    }

    @Test
    public void testAddAndGetWithNegativeOperand() {
        MutableLong mutableLong = new MutableLong(10L);
        long result = mutableLong.addAndGet(-3L);
        Assert.assertEquals(7L, result);
        Assert.assertEquals(7L, mutableLong.getValue().longValue());
    }

    @Test
    public void testAddAndGetWithLargeOperand() {
        MutableLong mutableLong = new MutableLong(Long.MAX_VALUE - 5);
        long result = mutableLong.addAndGet(5L);
        Assert.assertEquals(Long.MAX_VALUE, result);
        Assert.assertEquals(Long.MAX_VALUE, mutableLong.getValue().longValue());
    }

    @Test
    public void testAddAndGetThatCausesOverflow() {
        MutableLong mutableLong = new MutableLong(Long.MAX_VALUE);
        long result = mutableLong.addAndGet(1L);
        Assert.assertEquals(Long.MIN_VALUE, result);
        Assert.assertEquals(Long.MIN_VALUE, mutableLong.getValue().longValue());
    }
}