package org.apache.commons.lang3.mutable;

import org.junit.Test;
import static org.junit.Assert.*;

public class MutableByteTest {

    @Test
    public void testGetValue_DefaultConstructor() {
        // Arrange
        MutableByte mutableByte = new MutableByte();

        // Act
        Byte result = mutableByte.getValue();

        // Assert
        assertNotNull("The result should not be null.", result);
        assertEquals("The byte value should be 0.", Byte.valueOf((byte) 0), result);
    }

    @Test
    public void testGetValue_ByteConstructor() {
        // Arrange
        MutableByte mutableByte = new MutableByte((byte) 10);

        // Act
        Byte result = mutableByte.getValue();

        // Assert
        assertNotNull("The result should not be null.", result);
        assertEquals("The byte value should be 10.", Byte.valueOf((byte) 10), result);
    }

    @Test
    public void testGetValue_NumberConstructor() {
        // Arrange
        MutableByte mutableByte = new MutableByte(Integer.valueOf(20));

        // Act
        Byte result = mutableByte.getValue();

        // Assert
        assertNotNull("The result should not be null.", result);
        assertEquals("The byte value should be 20.", Byte.valueOf((byte) 20), result);
    }

    @Test
    public void testGetValue_StringConstructor() {
        // Arrange
        MutableByte mutableByte = new MutableByte("30");

        // Act
        Byte result = mutableByte.getValue();

        // Assert
        assertNotNull("The result should not be null.", result);
        assertEquals("The byte value should be 30.", Byte.valueOf((byte) 30), result);
    }
}