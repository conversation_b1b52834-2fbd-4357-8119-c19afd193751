package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import java.util.Collections;
import java.util.Set;  // Import the Set interface

public class PatternsTest {

    @Test
    public void testAnythingButMatch_withString() {
        // Setup
        final String input = "testString";

        // Invoke
        AnythingBut result = Patterns.anythingButMatch(input);

        // Assert
        assertNotNull(result);
        assertEquals(Collections.singleton(input), result.getValues());
        assertFalse(result.isNumeric());
    }
}

// Additional support classes for the test

class AnythingBut {
    private final Set<String> values;
    private final boolean isNumeric;

    public AnythingBut(Set<String> values, boolean isNumeric) {
        this.values = values;
        this.isNumeric = isNumeric;
    }

    public Set<String> getValues() {
        return values;
    }

    public boolean isNumeric() {
        return isNumeric;
    }
}

enum MatchType {
    EXACT, PREFIX, SUFFIX, PREFIX_EQUALS_IGNORE_CASE, SUFFIX_EQUALS_IGNORE_CASE, 
    ANYTHING_BUT_IGNORE_CASE, ANYTHING_BUT_PREFIX, ANYTHING_BUT_SUFFIX, 
    ANYTHING_BUT_WILDCARD, NUMERIC_EQ, EXISTS, ABSENT, EQUALS_IGNORE_CASE, WILDCARD
}