package com.google.gson;

// package com.google.gson;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import org.junit.Test;

public class GsonBuilderTest {

    @Test
    public void testDisableInnerClassSerialization() {
        // Create a new GsonBuilder instance
        GsonBuilder gsonBuilder = new GsonBuilder();

        // Disable inner class serialization
        GsonBuilder returnedBuilder = gsonBuilder.disableInnerClassSerialization();

        // Assert that the method returns the same instance (builder pattern)
        assertSame("disableInnerClassSerialization should return the same instance", gsonBuilder, returnedBuilder);

        // Create a new Gson instance
        Gson gson = gsonBuilder.create();
        
        // Assert that the Gson instance is not null
        assertNotNull("Gson instance should not be null", gson);
    }
}