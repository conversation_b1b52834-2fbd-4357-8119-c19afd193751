package org.apache.commons.lang3.mutable;

import org.junit.Test;
import static org.junit.Assert.*;

public class MutableFloatTest {

    @Test
    public void testAdd_Number_ValidOperand() {
        MutableFloat mutableFloat = new MutableFloat(5.5f);
        mutableFloat.add(Float.valueOf(2.2f));
        
        // Expected value after adding 2.2
        assertEquals(7.7f, mutableFloat.floatValue(), 0.0f);
    }

    @Test(expected = NullPointerException.class)
    public void testAdd_Number_NullOperand() {
        MutableFloat mutableFloat = new MutableFloat(5.5f);
        mutableFloat.add(null);  // Should throw NullPointerException
    }

    @Test
    public void testAdd_Number_ZeroOperand() {
        MutableFloat mutableFloat = new MutableFloat(3.0f);
        mutableFloat.add(Float.valueOf(0f));
        
        // Expected value remains unchanged
        assertEquals(3.0f, mutableFloat.floatValue(), 0.0f);
    }

    @Test
    public void testAdd_Number_NegativeOperand() {
        MutableFloat mutableFloat = new MutableFloat(3.0f);
        mutableFloat.add(Float.valueOf(-1.0f));
        
        // Expected value after subtracting 1.0
        assertEquals(2.0f, mutableFloat.floatValue(), 0.0f);
    }

    @Test
    public void testAdd_Number_LargeOperand() {
        MutableFloat mutableFloat = new MutableFloat(1.0f);
        mutableFloat.add(Float.valueOf(Float.MAX_VALUE));
        
        // Expected value after adding the largest float value
        assertEquals(Float.MAX_VALUE + 1.0f, mutableFloat.floatValue(), 0.0f);
    }
    
    @Test
    public void testAdd_Number_SpecialFloatValues() {
        MutableFloat mutableFloatNaN = new MutableFloat(Float.NaN);
        mutableFloatNaN.add(1.0f);
        
        // Value should remain NaN
        assertTrue(mutableFloatNaN.isNaN());

        MutableFloat mutableFloatPositiveInfinity = new MutableFloat(Float.POSITIVE_INFINITY);
        mutableFloatPositiveInfinity.add(1.0f);
        
        // Value should remain positive infinity
        assertTrue(mutableFloatPositiveInfinity.isInfinite() && mutableFloatPositiveInfinity.floatValue() > 0);

        MutableFloat mutableFloatNegativeInfinity = new MutableFloat(Float.NEGATIVE_INFINITY);
        mutableFloatNegativeInfinity.add(1.0f);
        
        // Value should remain negative infinity
        assertTrue(mutableFloatNegativeInfinity.isInfinite() && mutableFloatNegativeInfinity.floatValue() < 0);
    }
}