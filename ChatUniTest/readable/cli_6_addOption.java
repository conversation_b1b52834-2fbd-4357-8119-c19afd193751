package org.apache.commons.cli;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class OptionsTest {
    private Options options;
    private Option shortOnlyOption;
    private Option longOnlyOption;
    private Option requiredOption;

    @Before
    public void setUp() {
        options = new Options();
        shortOnlyOption = new Option("s", null, false, "Short only option");
        longOnlyOption = new Option("l", "longOption", false, "Long only option");
        requiredOption = new Option("r", "requiredOption", true, "Required option");
        requiredOption.setRequired(true);
    }

    @Test
    public void testAddShortOnlyOption() {
        options.addOption(shortOnlyOption);
        // Check that the option is added to shortOpts
        assertTrue(options.hasOption("s"));
        // Check that the option is not added to longOpts
        assertFalse(options.hasLongOption("l"));
        // Check that the option is not in the required list
        assertFalse(options.getRequiredOptions().contains("s"));
    }

    @Test
    public void testAddLongOnlyOption() {
        options.addOption(longOnlyOption);
        // Check that the option is added to longOpts
        assertTrue(options.hasLongOption("longOption"));
        // Check that the option is not added to shortOpts
        assertTrue(options.hasOption("l")); // it should also be present in shortOpts
        // Check that the option is not in the required list
        assertFalse(options.getRequiredOptions().contains("l"));
    }

    @Test
    public void testAddRequiredOption() {
        options.addOption(requiredOption);
        // Check that the option is added to shortOpts
        assertTrue(options.hasOption("r"));
        // Check that the option is added to longOpts
        assertTrue(options.hasLongOption("requiredOption")); 
        // Check that the option is marked as required
        assertTrue(options.getRequiredOptions().contains("r"));
    }

    @Test
    public void testAddOptionTwice() {
        final Option duplicateOption = new Option("r", "requiredOption", true, "Duplicate required option");
        duplicateOption.setRequired(true);
        options.addOption(requiredOption);
        options.addOption(duplicateOption);

        // Check that the option is added to shortOpts
        assertTrue(options.hasOption("r"));
        // Check that there's only one instance of the required option
        assertEquals(1, options.getRequiredOptions().stream().filter(opt -> opt.equals("r")).count());
    }
}