package org.apache.commons.lang3.mutable;

import org.junit.Test;
import static org.junit.Assert.*;

public class MutableBooleanTest {

    @Test
    public void testEquals_SameObject() {
        MutableBoolean mb = new MutableBoolean(true);
        // Assert that the object equals itself
        assertTrue(mb.equals(mb));
    }

    @Test
    public void testEquals_NullObject() {
        MutableBoolean mb = new MutableBoolean(true);
        // Assert that the object does not equal null
        assertFalse(mb.equals(null));
    }

    @Test
    public void testEquals_DifferentClass() {
        MutableBoolean mb = new MutableBoolean(true);
        // Assert that the object does not equal an instance of a different class
        assertFalse(mb.equals(new Object()));
    }

    @Test
    public void testEquals_EqualValues() {
        MutableBoolean mb1 = new MutableBoolean(true);
        MutableBoolean mb2 = new MutableBoolean(true);
        // Assert that two MutableBoolean objects with the same value are equal
        assertTrue(mb1.equals(mb2));
    }

    @Test
    public void testEquals_DifferentValues() {
        MutableBoolean mb1 = new MutableBoolean(true);
        MutableBoolean mb2 = new MutableBoolean(false);
        // Assert that two MutableBoolean objects with different values are not equal
        assertFalse(mb1.equals(mb2));
    }

    @Test
    public void testEquals_CoercedBoolean() {
        MutableBoolean mb1 = new MutableBoolean(true);
        Boolean bool = Boolean.TRUE;
        // Ensure that MutableBoolean and Boolean are not considered equal
        assertFalse(mb1.equals(bool));
    }
}