package org.apache.commons.lang3.mutable;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class MutableLongTest {

    /**
     * Test the compareTo method of MutableLong.
     * Covers cases where:
     * - this.value < other.value
     * - this.value == other.value
     * - this.value > other.value
     */
    @Test
    public void testCompareTo() {
        // Case 1: this.value < other.value
        MutableLong ml1 = new MutableLong(5);
        MutableLong ml2 = new MutableLong(10);
        assertEquals("Expected ml1 to be less than ml2", -1, ml1.compareTo(ml2));

        // Case 2: this.value == other.value
        ml1 = new MutableLong(5);
        ml2 = new MutableLong(5);
        assertEquals("Expected ml1 to be equal to ml2", 0, ml1.compareTo(ml2));

        // Case 3: this.value > other.value
        ml1 = new MutableLong(10);
        ml2 = new MutableLong(5);
        assertEquals("Expected ml1 to be greater than ml2", 1, ml1.compareTo(ml2));
    }

    /**
     * Test the compareTo method for edge cases.
     * - this.value is at Long.MIN_VALUE and other.value is at Long.MAX_VALUE
     * - this.value is at Long.MAX_VALUE and other.value is at Long.MIN_VALUE
     */
    @Test
    public void testCompareToEdgeCases() {
        // Case 4: this.value = Long.MIN_VALUE, other.value = Long.MAX_VALUE
        MutableLong ml1 = new MutableLong(Long.MIN_VALUE);
        MutableLong ml2 = new MutableLong(Long.MAX_VALUE);
        assertEquals("Expected ml1 to be less than ml2", -1, ml1.compareTo(ml2));

        // Case 5: this.value = Long.MAX_VALUE, other.value = Long.MIN_VALUE
        ml1 = new MutableLong(Long.MAX_VALUE);
        ml2 = new MutableLong(Long.MIN_VALUE);
        assertEquals("Expected ml1 to be greater than ml2", 1, ml1.compareTo(ml2));
    }

    /**
     * Test illegal argument scenario where other is null.
     */
    @Test(expected = NullPointerException.class)
    public void testCompareToNullInput() {
        MutableLong ml1 = new MutableLong(5);
        ml1.compareTo(null);
    }
}