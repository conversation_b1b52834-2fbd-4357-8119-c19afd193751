package com.google.gson;

import static org.junit.Assert.assertSame;
import org.junit.Test;

public class JsonPrimitiveTest {

    @Test
    public void testDeepCopy_Boolean() {
        JsonPrimitive booleanPrimitive = new JsonPrimitive(true);
        JsonPrimitive copy = booleanPrimitive.deepCopy();
        assertSame("deepCopy should return the same instance for boolean", booleanPrimitive, copy);
    }

    @Test
    public void testDeepCopy_Number() {
        JsonPrimitive numberPrimitive = new JsonPrimitive(42);
        JsonPrimitive copy = numberPrimitive.deepCopy();
        assertSame("deepCopy should return the same instance for number", numberPrimitive, copy);
    }

    @Test
    public void testDeepCopy_String() {
        JsonPrimitive stringPrimitive = new JsonPrimitive("example");
        JsonPrimitive copy = stringPrimitive.deepCopy();
        assertSame("deepCopy should return the same instance for string", stringPrimitive, copy);
    }

    @Test
    public void testDeepCopy_Character() {
        JsonPrimitive charPrimitive = new JsonPrimitive('c');
        JsonPrimitive copy = charPrimitive.deepCopy();
        assertSame("deepCopy should return the same instance for character", charPrimitive, copy);
    }
}