package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Collections;
import java.util.Set;
import java.util.HashSet;

public class PatternsTest {

    @Test
    public void testAnythingButNumberMatchWithSingleValue() {
        Set<Double> input = Collections.singleton(123.45);
        // Convert Double Set to String Set for method compatibility
        Set<String> inputAsStrings = new HashSet<>();
        input.forEach(d -> inputAsStrings.add(Double.toString(d)));

        // Call the method to test
        AnythingBut result = Patterns.anythingButNumbersMatch(inputAsStrings);

        // Assert that the AnythingBut object is not null
        assertNotNull("Resulting AnythingBut object should not be null", result);

        // Check the actual values contained in the AnythingBut to match the converted value
        String expectedValue = ComparableNumber.generate("123.45");
        assertTrue("Result should contain the normalized string '" + expectedValue + "'", result.getValues().contains(expectedValue));
    }

    @Test
    public void testAnythingButNumberMatchWithMultipleValues() {
        Set<Double> input = new HashSet<>();
        input.add(123.45);
        input.add(678.90);

        // Convert Double Set to String Set for method compatibility
        Set<String> inputAsStrings = new HashSet<>();
        input.forEach(d -> inputAsStrings.add(Double.toString(d)));

        // Call the method to test
        AnythingBut result = Patterns.anythingButNumbersMatch(inputAsStrings);

        // Assert that the result is not null
        assertNotNull("Resulting AnythingBut object should not be null", result);

        // Verify each of the expected normalized string values
        String expectedValue1 = ComparableNumber.generate("123.45");
        String expectedValue2 = ComparableNumber.generate("678.90");
        assertTrue("Result should contain the normalized string '" + expectedValue1 + "'", result.getValues().contains(expectedValue1));
        assertTrue("Result should contain the normalized string '" + expectedValue2 + "'", result.getValues().contains(expectedValue2));
    }

    @Test
    public void testAnythingButNumberMatchWithEmptySet() {
        Set<Double> input = Collections.emptySet();
        // Convert Double Set to String Set for method compatibility
        Set<String> inputAsStrings = new HashSet<>();

        // Call the method to test
        AnythingBut result = Patterns.anythingButNumbersMatch(inputAsStrings);

        // Assert that the result is not null
        assertNotNull("Resulting AnythingBut object should not be null", result);

        // Assert that the result has an empty set of values
        assertTrue("Result should be an empty set", result.getValues().isEmpty());
    }

    @Test(expected = NullPointerException.class)
    public void testAnythingButNumberMatchWithNullSet() {
        // Provide a null set to test the method's behavior
        Patterns.anythingButNumbersMatch(null);
    }
}