package org.apache.commons.lang3;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

import org.junit.Test;

public class ConversionTest {

    @Test
    public void testBinaryToHexDigit_0000() {
        boolean[] input = { false, false, false, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('0', result);
    }

    @Test
    public void testBinaryToHexDigit_1000() {
        boolean[] input = { true, false, false, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('1', result);
    }

    @Test
    public void testBinaryToHexDigit_0100() {
        boolean[] input = { false, true, false, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('2', result);
    }

    @Test
    public void testBinaryToHexDigit_0010() {
        boolean[] input = { false, false, true, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('4', result);
    }

    @Test
    public void testBinaryToHexDigit_0001() {
        boolean[] input = { false, false, false, true };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('8', result);
    }

    @Test
    public void testBinaryToHexDigit_1100() {
        boolean[] input = { true, true, false, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('3', result);
    }

    @Test
    public void testBinaryToHexDigit_1110() {
        boolean[] input = { true, true, true, false };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('7', result);
    }

    @Test
    public void testBinaryToHexDigit_1111() {
        boolean[] input = { true, true, true, true };
        char result = Conversion.binaryToHexDigit(input, 0);
        assertEquals('f', result);
    }

    @Test
    public void testBinaryToHexDigit_EmptyArray() {
        boolean[] input = {};
        assertThrows(IllegalArgumentException.class, () -> {
            Conversion.binaryToHexDigit(input, 0);
        });
    }

    @Test
    public void testBinaryToHexDigit_NullInput() {
        assertThrows(NullPointerException.class, () -> {
            Conversion.binaryToHexDigit(null, 0);
        });
    }

    @Test
    public void testBinaryToHexDigit_OffsetConversion() {
        boolean[] input = { false, false, false, true, // Representing '8'
                             true, false, false, false // Representing '1'
                           };
        char result = Conversion.binaryToHexDigit(input, 4);
        assertEquals('1', result);
    }
}