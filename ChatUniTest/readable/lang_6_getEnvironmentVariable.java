package org.apache.commons.lang3;

import static org.junit.Assert.*;

import org.junit.Test;

public class SystemUtilsTest {

    /**
     * Test when the environment variable exists.
     */
    @Test
    public void testGetEnvironmentVariableExists() {
        String existingEnvVariable = "PATH"; // Common environment variable
        String defaultValue = "default";

        String result = SystemUtils.getEnvironmentVariable(existingEnvVariable, defaultValue);

        assertNotNull("The result should not be null", result);
        assertNotEquals("The result should not be the default value when the environment variable exists", defaultValue, result);
    }

    /**
     * Test when the environment variable does not exist.
     */
    @Test
    public void testGetEnvironmentVariableDoesNotExist() {
        String nonExistingEnvVariable = "NON_EXISTING_ENV_VARIABLE";
        String defaultValue = "default";

        String result = SystemUtils.getEnvironmentVariable(nonExistingEnvVariable, defaultValue);

        assertEquals("The result should be the default value when the environment variable does not exist", defaultValue, result);
    }

    /**
     * Test default handling for when a SecurityException is expected.
     * Requires setting up a security manager which restricts access to environment variables.
     */
    @Test
    public void testGetEnvironmentVariableSecurityException() {
        System.setSecurityManager(new SecurityManager() {
            @Override
            public void checkPermission(java.security.Permission perm) {
                if ("getenv.*".equals(perm.getName())) {
                    throw new SecurityException("Security Manager does not allow access to environment variables!");
                }
            }
        });

        String envVariable = "ANY_ENV_VARIABLE";
        String defaultValue = "default";

        String result = SystemUtils.getEnvironmentVariable(envVariable, defaultValue);

        assertEquals("The result should be the default value when access is restricted by a SecurityException", defaultValue, result);

        System.setSecurityManager(null); // Reset security manager
    }
}