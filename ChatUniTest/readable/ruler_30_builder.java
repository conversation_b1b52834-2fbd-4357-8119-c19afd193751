package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;

public class GenericMachineTest {

    @Test
    public void testBuilderCreatesGenericMachine() {
        // Create a builder using the builder method
        GenericMachine.Builder<GenericMachine<String>, String> builder = GenericMachine.builder();

        // Check that the builder is not null
        assertNotNull("Builder should not be null", builder);

        // Use the builder to build a GenericMachine without additional state reuse
        GenericMachine<String> genericMachine = builder.build();

        // Assertions to verify that a GenericMachine object was made properly
        assertNotNull("GenericMachine should not be null", genericMachine);
        assertTrue("GenericMachine should be empty", genericMachine.isEmpty());

        // Additional tests could go here if there are other accessible properties or methods to verify
    }
}