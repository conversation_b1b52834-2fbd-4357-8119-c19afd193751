package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import javax.annotation.Nonnull;

// Mock classes for the purpose of testing
class Patterns {
    // Assume this class has a meaningful implementation relevant for the application.
}

class NameState {
    private String stateName;

    public NameState(String stateName) {
        this.stateName = stateName;
    }

    public String getStateName() {
        return stateName;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        NameState nameState = (NameState) obj;
        return stateName != null ? stateName.equals(nameState.stateName) : nameState.stateName == null;
    }

    @Override
    public int hashCode() {
        return stateName != null ? stateName.hashCode() : 0;
    }
}

public class SingleStateNameMatcherTest {

    private SingleStateNameMatcher matcher;
    private Patterns pattern;
    private NameState state1;
    private NameState state2;

    @Before
    public void setUp() {
        matcher = new SingleStateNameMatcher();
        pattern = new Patterns();
        state1 = new NameState("State1");
        state2 = new NameState("State2");
    }

    @Test
    public void testAddPatternWhenStateIsNull() {
        // Test when the internal state is null
        NameState returnedState = matcher.addPattern(pattern, state1);
        assertNotNull("The returned state should not be null", returnedState);
        assertEquals("The internal state should be set and returned as state1", state1, returnedState);
        assertEquals("The internal state should match the returned state", matcher.getNextState(), returnedState);
    }

    @Test
    public void testAddPatternWhenStateIsNotNull() {
        // Set the initial state to state1
        matcher.addPattern(pattern, state1);

        // Try to add a new state when the internal state is already set
        NameState returnedState = matcher.addPattern(pattern, state2);

        // Assertions
        assertNotNull("The returned state should not be null", returnedState);
        assertEquals("The internal state should remain as state1", state1, returnedState);
        assertNotEquals("The internal state should not change to state2", state2, returnedState);
        assertEquals("The internal state should still be state1", matcher.getNextState(), state1);
    }
}