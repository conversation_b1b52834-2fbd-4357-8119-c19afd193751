package org.apache.commons.cli;

// package org.apache.commons.cli;

import static org.junit.Assert.*;
import org.junit.Test;
import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import org.apache.commons.cli.ParseException;

// Mock the PatternOptionBuilder for testing purposes
class PatternOptionBuilder {
    public static final Class<String> STRING_VALUE = String.class;
    public static final Class<Object> OBJECT_VALUE = Object.class;
    public static final Class<Number> NUMBER_VALUE = Number.class;
    public static final Class<java.util.Date> DATE_VALUE = java.util.Date.class;
    public static final Class<Class> CLASS_VALUE = Class.class;  // Fixed here
    public static final Class<File> FILE_VALUE = File.class;
    public static final Class<FileInputStream> EXISTING_FILE_VALUE = FileInputStream.class;
    public static final Class<File[]> FILES_VALUE = File[].class;
    public static final Class<URL> URL_VALUE = URL.class;
}

public class TypeHandlerTest {

    @Test
    public void testCreateValueWithString() throws ParseException {
        String value = "testString";
        Object result = TypeHandler.createValue(value, PatternOptionBuilder.STRING_VALUE);
        assertEquals(value, result);
    }

    @Test
    public void testCreateValueWithNumber() throws ParseException {
        String value = "123.45";
        Object result = TypeHandler.createValue(value, PatternOptionBuilder.NUMBER_VALUE);
        assertTrue(result instanceof Double);
        assertEquals(123.45, result);
    }

    @Test
    public void testCreateValueWithClass() throws ParseException {
        String value = "java.lang.String";
        Object result = TypeHandler.createValue(value, PatternOptionBuilder.CLASS_VALUE);
        assertEquals(String.class, result);
    }

    @Test
    public void testCreateValueWithFile() throws ParseException {
        String filePath = "sample.txt";
        File file = new File(filePath);
        Object result = TypeHandler.createValue(filePath, PatternOptionBuilder.FILE_VALUE);
        assertTrue(result instanceof File);
        assertEquals(file, result);
    }
    
    @Test
    public void testCreateValueWithURL() throws ParseException {
        String urlString = "http://example.com";
        Object result = TypeHandler.createValue(urlString, PatternOptionBuilder.URL_VALUE);
        assertTrue(result instanceof URL);
        assertEquals(urlString, result.toString());
    }

    @Test
    public void testCreateValueWithObject() throws ParseException {
        String className = "java.util.Date";
        try {
            Object result = TypeHandler.createValue(className, PatternOptionBuilder.OBJECT_VALUE);
            assertTrue(result instanceof java.util.Date);
        } catch (ParseException e) {
            fail("Exception thrown in createObject: " + e.getMessage());
        }
    }

    @Test(expected = ParseException.class)
    public void testCreateValueWithInvalidObject() throws ParseException {
        String className = "non.existing.ClassName";
        TypeHandler.createValue(className, PatternOptionBuilder.OBJECT_VALUE);
    }

    @Test(expected = ParseException.class)
    public void testCreateValueWithInvalidURL() throws ParseException {
        String malformedUrl = ":::malformed:::";
        TypeHandler.createValue(malformedUrl, PatternOptionBuilder.URL_VALUE);
    }

    @Test(expected = ParseException.class)
    public void testCreateValueWithInvalidNumber() throws ParseException {
        String invalidNumber = "notANumber";
        TypeHandler.createValue(invalidNumber, PatternOptionBuilder.NUMBER_VALUE);
    }
}