package org.apache.commons.lang3.mutable;

// package org.apache.commons.lang3.mutable;

import static org.junit.Assert.assertEquals;
import org.junit.Test;

public class MutableByteTest {

    @Test
    public void testHashCodeDefaultConstructor() {
        MutableByte mutableByte = new MutableByte();
        assertEquals("Default constructor hash code should be 0", 0, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeByteConstructor() {
        MutableByte mutableByte = new MutableByte((byte) 127);
        assertEquals("Hash code should match the byte value", 127, mutableByte.hashCode());

        mutableByte = new MutableByte((byte) -128);
        assertEquals("Hash code should match the byte value", -128, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeNumberConstructor() {
        // Correcting the Number constructor test case to use Integer.valueOf for boxing
        MutableByte mutableByte = new MutableByte(Integer.valueOf(64));
        assertEquals("Hash code should match the byte value", 64, mutableByte.hashCode());

        mutableByte = new MutableByte(Integer.valueOf(-64));
        assertEquals("Hash code should match the byte value", -64, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeStringConstructor() {
        MutableByte mutableByte = new MutableByte("32");
        assertEquals("Hash code should match the byte value", 32, mutableByte.hashCode());

        mutableByte = new MutableByte("-32");
        assertEquals("Hash code should match the byte value", -32, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeAfterIncrement() {
        MutableByte mutableByte = new MutableByte((byte) 5);
        mutableByte.increment();
        assertEquals("Hash code should reflect incremented value", 6, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeAfterDecrement() {
        MutableByte mutableByte = new MutableByte((byte) 5);
        mutableByte.decrement();
        assertEquals("Hash code should reflect decremented value", 4, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeAfterSetValue() {
        MutableByte mutableByte = new MutableByte();
        mutableByte.setValue((byte) 100);
        assertEquals("Hash code should reflect newly set value", 100, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeAfterAdd() {
        MutableByte mutableByte = new MutableByte((byte) 10);
        mutableByte.add((byte) 5);
        assertEquals("Hash code should reflect added value", 15, mutableByte.hashCode());
    }

    @Test
    public void testHashCodeAfterSubtract() {
        MutableByte mutableByte = new MutableByte((byte) 10);
        mutableByte.subtract((byte) 3);
        assertEquals("Hash code should reflect subtracted value", 7, mutableByte.hashCode());
    }
}