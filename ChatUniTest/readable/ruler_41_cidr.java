package software.amazon.event.ruler;

// package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Test;

public class CIDRTest {

    @Test(expected = IllegalArgumentException.class)
    public void testMalformedCIDROneSlashRequired() {
        CIDR.cidr("***********");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testMalformedCIDRMaskMustBeInteger() {
        CIDR.cidr("***********/abc");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testMalformedCIDRMaskMustNotBeNegative() {
        CIDR.cidr("***********/-1");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testIPv4MaskBitsExceedLimit() {
        CIDR.cidr("***********/32");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testIPv6MaskBitsExceedLimit() {
        CIDR.cidr("2001:db8::/129");
    }

    @Test
    public void testValidIPv4CIDR() {
        // Assuming `Range` class has properties like open bottom, open top, is CIDR
        Range range = CIDR.cidr("***********/24");
        assertNotNull(range);
        
        // Placeholder assertions, replace with valid checks if available
        // assertFalse(range.isBottomOpen());
        // assertFalse(range.isTopOpen());
        // assertTrue(range.isCidrEnabled());
    }

    @Test
    public void testValidIPv6CIDR() {
        // Assuming `Range` class has properties like open bottom, open top, is CIDR
        Range range = CIDR.cidr("2001:db8::/64");
        assertNotNull(range);
        
        // Placeholder assertions, replace with valid checks if available
        // assertFalse(range.isBottomOpen());
        // assertFalse(range.isTopOpen());
        // assertTrue(range.isCidrEnabled());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testInvalidIPFormat() {
        CIDR.cidr("invalidIP/24");
    }
}