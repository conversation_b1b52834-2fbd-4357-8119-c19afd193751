package org.jfree.chart.ui;

import static org.junit.Assert.*;
import org.junit.Test;

public class TextAnchorTest {

    @Test
    public void testIsHorizontalCenter() {
        // Test for BASELINE_CENTER
        assertTrue("BASELINE_CENTER should be a horizontal center anchor", TextAnchor.BASELINE_CENTER.isHorizontalCenter());
        
        // Test for BOTTOM_CENTER
        assertTrue("BOTTOM_CENTER should be a horizontal center anchor", TextAnchor.BOTTOM_CENTER.isHorizontalCenter());
        
        // Test for CENTER
        assertTrue("CENTER should be a horizontal center anchor", TextAnchor.CENTER.isHorizontalCenter());
        
        // Test for HALF_ASCENT_CENTER
        assertTrue("HALF_ASCENT_CENTER should be a horizontal center anchor", TextAnchor.HALF_ASCENT_CENTER.isHorizontalCenter());
        
        // Test for TOP_CENTER
        assertTrue("TOP_CENTER should be a horizontal center anchor", TextAnchor.TOP_CENTER.isHorizontalCenter());
        
        // Check non-center anchors
        assertFalse("TOP_LEFT should not be a horizontal center anchor", TextAnchor.TOP_LEFT.isHorizontalCenter());
        assertFalse("TOP_RIGHT should not be a horizontal center anchor", TextAnchor.TOP_RIGHT.isHorizontalCenter());
        assertFalse("HALF_ASCENT_LEFT should not be a horizontal center anchor", TextAnchor.HALF_ASCENT_LEFT.isHorizontalCenter());
        assertFalse("HALF_ASCENT_RIGHT should not be a horizontal center anchor", TextAnchor.HALF_ASCENT_RIGHT.isHorizontalCenter());
        assertFalse("CENTER_LEFT should not be a horizontal center anchor", TextAnchor.CENTER_LEFT.isHorizontalCenter());
        assertFalse("CENTER_RIGHT should not be a horizontal center anchor", TextAnchor.CENTER_RIGHT.isHorizontalCenter());
        assertFalse("BASELINE_LEFT should not be a horizontal center anchor", TextAnchor.BASELINE_LEFT.isHorizontalCenter());
        assertFalse("BASELINE_RIGHT should not be a horizontal center anchor", TextAnchor.BASELINE_RIGHT.isHorizontalCenter());
        assertFalse("BOTTOM_LEFT should not be a horizontal center anchor", TextAnchor.BOTTOM_LEFT.isHorizontalCenter());
        assertFalse("BOTTOM_RIGHT should not be a horizontal center anchor", TextAnchor.BOTTOM_RIGHT.isHorizontalCenter());
    }

}