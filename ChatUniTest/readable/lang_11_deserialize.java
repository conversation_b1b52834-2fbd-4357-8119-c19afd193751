package org.apache.commons.lang3;

// package org.apache.commons.lang3;

import static org.junit.Assert.*;

import org.junit.Test;
import java.io.Serializable;

public class SerializationUtilsTest {

    // A simple Serializable class for testing
    private static class TestObject implements Serializable {
        private static final long serialVersionUID = 1L;
        private String name;

        public TestObject(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return name.equals(that.name);
        }

        @Override
        public int hashCode() {
            return name.hashCode();
        }
    }

    @Test(expected = NullPointerException.class)  // Changed from IllegalArgumentException to NullPointerException
    public void testDeserializeWithNullByteArray() {
        SerializationUtils.deserialize((byte[]) null);
    }

    @Test
    public void testDeserializeValidObject() {
        TestObject originalObject = new TestObject("TestName");
        byte[] serializedData = SerializationUtils.serialize(originalObject);

        TestObject deserializedObject = SerializationUtils.deserialize(serializedData);

        assertNotNull(deserializedObject);
        assertEquals("Deserialized object should be equal to original object", originalObject, deserializedObject);
    }

    @Test(expected = SerializationException.class)
    public void testDeserializeInvalidData() {
        byte[] invalidData = new byte[]{1, 2, 3};  // Random bytes that do not make up a serialized object
        SerializationUtils.deserialize(invalidData);
    }
}