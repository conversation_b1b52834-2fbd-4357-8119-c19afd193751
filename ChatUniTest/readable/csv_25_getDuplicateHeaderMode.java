package org.apache.commons.csv;

import static org.junit.Assert.assertEquals;
import org.junit.Test;

public class CSVFormatTest {

    @Test
    public void testGetDuplicateHeaderModeDefault() {
        // Test default duplicate header mode
        CSVFormat format = CSVFormat.DEFAULT;
        assertEquals("The default duplicate header mode should be ALLOW_ALL", 
                     DuplicateHeaderMode.ALLOW_ALL, 
                     format.getDuplicateHeaderMode());
    }

    @Test
    public void testGetDuplicateHeaderModeAllowAll() {
        // Test duplicate header mode with ALLOW_ALL setting
        CSVFormat.Builder builder = CSVFormat.DEFAULT.builder();
        builder.setDuplicateHeaderMode(DuplicateHeaderMode.ALLOW_ALL);
        CSVFormat format = builder.build();
        assertEquals("Setting duplicate header mode to ALLOW_ALL should be reflected", 
                     DuplicateHeaderMode.ALLOW_ALL, 
                     format.getDuplicateHeaderMode());
    }

    @Test
    public void testGetDuplicateHeaderModeAllowEmpty() {
        // Test duplicate header mode with ALLOW_EMPTY setting
        CSVFormat.Builder builder = CSVFormat.DEFAULT.builder();
        builder.setDuplicateHeaderMode(DuplicateHeaderMode.ALLOW_EMPTY);
        CSVFormat format = builder.build();
        assertEquals("Setting duplicate header mode to ALLOW_EMPTY should be reflected", 
                     DuplicateHeaderMode.ALLOW_EMPTY, 
                     format.getDuplicateHeaderMode());
    }

    @Test
    public void testGetDuplicateHeaderModeDisallow() {
        // Test duplicate header mode with DISALLOW setting
        CSVFormat.Builder builder = CSVFormat.DEFAULT.builder();
        builder.setDuplicateHeaderMode(DuplicateHeaderMode.DISALLOW);
        CSVFormat format = builder.build();
        assertEquals("Setting duplicate header mode to DISALLOW should be reflected", 
                     DuplicateHeaderMode.DISALLOW, 
                     format.getDuplicateHeaderMode());
    }
}