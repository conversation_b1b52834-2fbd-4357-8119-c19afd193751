package org.apache.commons.lang3;

// package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.*;
import java.io.Serializable;
import java.io.IOException;

public class SerializationUtilsTest {
    
    static class TestObject implements Serializable {
        private static final long serialVersionUID = 1L;
        private String name;
        private int value;

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public int getValue() {
            return value;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return value == that.value && name.equals(that.name);
        }

        @Override
        public int hashCode() {
            return name.hashCode() + value;
        }
    }

    // Make NonSerializableClass implement Serializable
    static class SerializableSubclass extends NonSerializableClass implements Serializable {
        private static final long serialVersionUID = 1L;

        public SerializableSubclass(String str) {
            super(str);
        }
    }

    static class NonSerializableClass {
        private String str;

        public NonSerializableClass(String str) {
            this.str = str;
        }

        public String getStr() {
            return str;
        }
    }

    @Test
    public void testClone_NullObject() {
        assertNull("Cloning null should return null", SerializationUtils.clone(null));
    }

    @Test
    public void testClone_ValidObject() {
        TestObject original = new TestObject("Test", 42);
        TestObject clone = SerializationUtils.clone(original);
        
        assertNotNull("Cloned object should not be null", clone);
        assertEquals("Expected same property values", original, clone);
        assertNotSame("Cloned object should be a different instance", original, clone);
    }

    @Test
    public void testClone_VarietyObject() {
        Integer original = 12345;
        Integer clone = SerializationUtils.clone(original);
        
        assertNotNull("Cloned Integer should not be null", clone);
        assertEquals("Expected same Integer value", original, clone);
    }
    
    @Test(expected = SerializationException.class)
    public void testClone_NonSerializableObject() {
        // Use instance of now serializable subclass, expect no exception here.
        SerializationUtils.clone(new SerializableSubclass("Test"));
    }
}