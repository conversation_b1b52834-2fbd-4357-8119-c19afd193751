package org.apache.commons.cli;

// package org.apache.commons.cli;

import org.junit.Test;
import org.junit.Before;

import static org.junit.Assert.*;

public class OptionTest {

    private Option option;

    @Before
    public void setup() {
        option = new Option("a", false, "test option");
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testAddValueThrowsUnsupportedOperationException() {
        option.addValue("testValue");
    }

    @Test
    public void testAddValueThrowsExceptionWithCorrectMessage() {
        try {
            option.addValue("testValue");
            fail("Expected an UnsupportedOperationException to be thrown");
        } catch (UnsupportedOperationException e) {
            assertEquals("The addValue method is not intended for client use. Subclasses should use the addValueForProcessing method instead. ", e.getMessage());
        }
    }
}