package com.google.gson;

// package com.google.gson;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.ArgumentMatchers;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.google.gson.ExclusionStrategy;

public class GsonBuilderTest {
    private GsonBuilder gsonBuilder;
    private ExclusionStrategy mockStrategy;

    @Before
    public void setUp() {
        gsonBuilder = new GsonBuilder();
        mockStrategy = mock(ExclusionStrategy.class);
        gsonBuilder = gsonBuilder.setExclusionStrategies(mockStrategy);
    }

    @Test
    public void testAddSerializationExclusionStrategy_NullStrategy() {
        // Test Null Pointer Exception when passing null strategy
        try {
            gsonBuilder.addSerializationExclusionStrategy(null);
        } catch (NullPointerException e) {
            // expected outcome
        }
    }

    @Test
    public void testAddSerializationExclusionStrategy_ValidStrategy() {
        GsonBuilder result = gsonBuilder.addSerializationExclusionStrategy(mockStrategy);
        // Assuming you verify indirectly or with actual behavior since Excluder is not directly mockable
        assertSame("Returned instance should be same as the builder instance", gsonBuilder, result);
        verify(mockStrategy, Mockito.never()).shouldSkipClass(ArgumentMatchers.any(Class.class));  // Example of verifying mock strategy behavior if applicable
    }

    @Test
    public void testChainAddSerializationExclusionStrategy() {
        // Mock behavior for multiple strategies
        ExclusionStrategy mockStrategy2 = mock(ExclusionStrategy.class);

        // Chain multiple calls
        gsonBuilder.addSerializationExclusionStrategy(mockStrategy)
                   .addSerializationExclusionStrategy(mockStrategy2);

        // Ensure no exceptions and logically assume correct chaining
    }
}