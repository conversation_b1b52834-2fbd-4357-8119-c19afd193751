package org.jfree.chart.ui;

// package org.jfree.chart.ui;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import javax.swing.*;

public class PaintSampleTest {

    private PaintSample paintSample;
    private Graphics2D graphics2D;

    // Subclass to expose the protected paintComponent method for testing
    private class PaintSampleTestable extends PaintSample {
        public PaintSampleTestable(Paint paint) {
            super(paint);
        }

        @Override
        public void paintComponent(Graphics g) {
            super.paintComponent(g);
        }
    }

    @Before
    public void setUp() {
        // Use a mock Paint object for testing
        Paint mockPaint = mock(Paint.class);
        paintSample = new PaintSampleTestable(mockPaint);

        // Mock Graphics2D as well
        graphics2D = mock(Graphics2D.class);
    }

    @Test
    public void testPaintComponentWithValidGraphics() {
        // Mock Dimension and Insets to cover all branches
        Dimension mockSize = new Dimension(100, 50);
        Insets mockInsets = new Insets(5, 5, 5, 5);

        // Override the getSize() and getInsets() methods to return our mock values
        JComponent spyPaintSample = spy(paintSample);
        doReturn(mockSize).when(spyPaintSample).getSize();
        doReturn(mockInsets).when(spyPaintSample).getInsets();

        // Use the subclass to call the method directly
        ((PaintSampleTestable) spyPaintSample).paintComponent(graphics2D);

        // Calculate expected values for assertions
        double xx = mockInsets.left;
        double yy = mockInsets.top;
        double ww = (mockSize.getWidth() - mockInsets.left - mockInsets.right) - 1;
        double hh = (mockSize.getHeight() - mockInsets.top - mockInsets.bottom) - 1;

        // Assert that setPaint and fill were called on the graphics object with correct parameters
        verify(graphics2D).setPaint(paintSample.getPaint());
        verify(graphics2D).fill(new Rectangle2D.Double(xx, yy, ww, hh));

        // Assert that the color was set to black for the border
        verify(graphics2D).setPaint(Color.BLACK);
        verify(graphics2D).draw(new Rectangle2D.Double(xx, yy, ww, hh));
    }
}