package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;

public class TextAnchorTest {

    @Test
    public void testIsHalfAscentForHalfAscentLeft() {
        // TextAnchor.HALF_ASCENT_LEFT should return true for isHalfAscent()
        assertTrue(TextAnchor.HALF_ASCENT_LEFT.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForHalfAscentCenter() {
        // TextAnchor.HALF_ASCENT_CENTER should return true for isHalfAscent()
        assertTrue(TextAnchor.HALF_ASCENT_CENTER.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForHalfAscentRight() {
        // TextAnchor.HALF_ASCENT_RIGHT should return true for isHalfAscent()
        assertTrue(TextAnchor.HALF_ASCENT_RIGHT.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForTopLeft() {
        // TextAnchor.TOP_LEFT should NOT return true for isHalfAscent()
        assertFalse(TextAnchor.TOP_LEFT.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForBaselineLeft() {
        // TextAnchor.BASELINE_LEFT should NOT return true for isHalfAscent()
        assertFalse(TextAnchor.BASELINE_LEFT.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForCenter() {
        // TextAnchor.CENTER should NOT return true for isHalfAscent()
        assertFalse(TextAnchor.CENTER.isHalfAscent());
    }

    @Test
    public void testIsHalfAscentForBottomRight() {
        // TextAnchor.BOTTOM_RIGHT should NOT return true for isHalfAscent()
        assertFalse(TextAnchor.BOTTOM_RIGHT.isHalfAscent());
    }
}