
import json
import os
import random
from collections import defaultdict

record_list = ["/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/chart-0330-gpt_4o",
               "/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/cli-0324-gpt_4o",
               "/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/csv-0324-gpt_4o",
               "/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/gson-0323-gpt_4o",
               "/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/lang-0330-gpt_4o",
               "/Users/<USER>/Desktop/TestAgents/ChatUniTest/new_result/ruler-0324-gpt_4o"]

def extract_function_name(name):
    """从name字段中提取函数名（去除数字前缀）"""
    parts = name.split('_', 1)
    if len(parts) > 1:
        return parts[1]  # 返回下划线后的部分
    return name

def load_test_cases(jsonl_path):
    """从jsonl文件中加载测试用例"""
    test_cases = []
    try:
        with open(jsonl_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    test_case = json.loads(line)
                    # 只处理有test_case内容的记录
                    if test_case.get('test_case') and len(test_case['test_case']) > 0:
                        test_cases.append(test_case)
    except FileNotFoundError:
        print(f"文件未找到: {jsonl_path}")
    except Exception as e:
        print(f"读取文件时出错 {jsonl_path}: {e}")

    return test_cases

def select_test_cases(test_cases, max_count=16):
    """
    选择测试用例，确保函数名唯一性
    """
    # 按函数名分组
    function_groups = defaultdict(list)
    for test_case in test_cases:
        func_name = extract_function_name(test_case['name'])
        function_groups[func_name].append(test_case)

    selected_cases = []

    # 首先从每个函数名组中选择一个测试用例
    for func_name, cases in function_groups.items():
        if len(selected_cases) < max_count:
            # 随机选择一个测试用例
            selected_case = random.choice(cases)
            selected_cases.append(selected_case)

    # 如果还没有达到16个，从剩余的测试用例中随机选择
    if len(selected_cases) < max_count:
        remaining_cases = []
        selected_names = {case['name'] for case in selected_cases}

        for test_case in test_cases:
            if test_case['name'] not in selected_names:
                remaining_cases.append(test_case)

        # 随机选择剩余的测试用例
        additional_count = min(max_count - len(selected_cases), len(remaining_cases))
        if additional_count > 0:
            additional_cases = random.sample(remaining_cases, additional_count)
            selected_cases.extend(additional_cases)

    return selected_cases[:max_count]

def get_project_name(directory_path):
    """从目录路径中提取项目名称"""
    dir_name = os.path.basename(directory_path)
    # 提取项目名称（去除日期和模型信息）
    parts = dir_name.split('-')
    if len(parts) > 0:
        return parts[0]
    return dir_name

def save_test_case(test_case, output_dir, project_name):
    """保存单个测试用例到文件"""
    # 创建文件名：项目_名称.java
    filename = f"{project_name}_{test_case['name']}.java"
    filepath = os.path.join(output_dir, filename)

    # 获取测试用例内容（取第一个test_case）
    test_content = test_case['test_case'][0] if test_case['test_case'] else ""

    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"已保存: {filename}")
    except Exception as e:
        print(f"保存文件时出错 {filename}: {e}")

def main():
    """主函数"""
    output_dir = "/Users/<USER>/Desktop/TestAgents/ChatUniTest/readable"

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 设置随机种子以便结果可重现（可选）
    random.seed(42)

    for directory in record_list:
        print(f"\n处理目录: {directory}")

        # 构建test_case.jsonl文件路径
        jsonl_path = os.path.join(directory, "test_case.jsonl")

        # 加载测试用例
        test_cases = load_test_cases(jsonl_path)
        print(f"加载了 {len(test_cases)} 个测试用例")

        if not test_cases:
            print("没有找到有效的测试用例，跳过此目录")
            continue

        # 选择测试用例
        selected_cases = select_test_cases(test_cases, 16)
        print(f"选择了 {len(selected_cases)} 个测试用例")

        # 获取项目名称
        project_name = get_project_name(directory)

        # 保存选择的测试用例
        for test_case in selected_cases:
            save_test_case(test_case, output_dir, project_name)

        print(f"项目 {project_name} 处理完成")

    print(f"\n所有测试用例已保存到: {output_dir}")

if __name__ == "__main__":
    main()
